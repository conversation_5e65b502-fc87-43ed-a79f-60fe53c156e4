package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.dto.MemberSyncInput;
import com.extracme.evcard.mmp.service.IAgencyService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * MemberSyncController 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class MemberSyncControllerTest {

    @Mock
    private IAgencyService agencyService;

    @InjectMocks
    private MemberSyncController memberSyncController;

    private MemberSyncInput validInput;

    @Before
    public void setUp() {
        validInput = new MemberSyncInput();
        validInput.setAgencyName("测试企业");
        validInput.setContactName("张三");
        validInput.setContactMobile("13800138000");
        validInput.setLicenseNo("91110000000000000X");
        validInput.setTel("010-12345678");
        validInput.setEmail("<EMAIL>");
        validInput.setCooperateStatus(1);
        validInput.setMaxCarNumber(10);
        validInput.setDiscountRate(95.0);
        validInput.setBeneficiaryNumber(50);
        validInput.setExemptDepositAmount(new BigDecimal("1000"));
        validInput.setCreateWay(3);
        validInput.setOperatorId(1L);
        validInput.setOperatorName("测试操作员");
    }

    @Test
    public void testMemberSync_Success() {
        // Mock成功响应
        BaseResponse mockResponse = new BaseResponse(0, "企业会员创建成功", "AGENCY001");
        when(agencyService.memberSync(any(MemberSyncInput.class))).thenReturn(mockResponse);

        DefaultWebRespVO result = memberSyncController.memberSync(validInput);

        assertEquals("0", result.getCode());
        assertEquals("企业会员创建成功", result.getMessage());
        assertEquals("AGENCY001", result.getData());
        verify(agencyService).memberSync(any(MemberSyncInput.class));
    }

    @Test
    public void testMemberSync_BusinessError() {
        // Mock业务错误响应
        BaseResponse mockResponse = new BaseResponse(-1, "企业名称不能为空");
        when(agencyService.memberSync(any(MemberSyncInput.class))).thenReturn(mockResponse);

        DefaultWebRespVO result = memberSyncController.memberSync(validInput);

        assertEquals("-1", result.getCode());
        assertEquals("企业名称不能为空", result.getMessage());
        verify(agencyService).memberSync(any(MemberSyncInput.class));
    }

    @Test
    public void testMemberSync_Exception() {
        // Mock异常
        when(agencyService.memberSync(any(MemberSyncInput.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        DefaultWebRespVO result = memberSyncController.memberSync(validInput);

        assertEquals("-1", result.getCode());
        assertTrue(result.getMessage().contains("会员同步失败"));
        verify(agencyService).memberSync(any(MemberSyncInput.class));
    }

    @Test
    public void testMemberSync_NullInput() {
        // Mock空输入的响应
        BaseResponse mockResponse = new BaseResponse(-1, "输入参数不能为空");
        when(agencyService.memberSync(any())).thenReturn(mockResponse);

        DefaultWebRespVO result = memberSyncController.memberSync(null);

        assertEquals("-1", result.getCode());
        assertEquals("输入参数不能为空", result.getMessage());
        verify(agencyService).memberSync(any());
    }

    @Test
    public void testMemberSync_SuccessWithoutData() {
        // Mock成功响应但没有数据
        BaseResponse mockResponse = new BaseResponse(0, "企业会员已创建，信息已更新");
        when(agencyService.memberSync(any(MemberSyncInput.class))).thenReturn(mockResponse);

        DefaultWebRespVO result = memberSyncController.memberSync(validInput);

        assertEquals("0", result.getCode());
        assertEquals("企业会员已创建，信息已更新", result.getMessage());
        assertNull(result.getData());
        verify(agencyService).memberSync(any(MemberSyncInput.class));
    }

    @Test
    public void testMemberSync_PartialSuccess() {
        // Mock部分成功的响应（例如企业创建成功但用车规则创建失败）
        BaseResponse mockResponse = new BaseResponse(0, "企业会员创建成功，但用车规则创建失败", "AGENCY001");
        when(agencyService.memberSync(any(MemberSyncInput.class))).thenReturn(mockResponse);

        DefaultWebRespVO result = memberSyncController.memberSync(validInput);

        assertEquals("0", result.getCode());
        assertEquals("企业会员创建成功，但用车规则创建失败", result.getMessage());
        assertEquals("AGENCY001", result.getData());
        verify(agencyService).memberSync(any(MemberSyncInput.class));
    }
}
