package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.config.UserAuthRequired;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IAgencyService;
import com.extracme.evcard.mmp.service.IMmpAgencyAccountService;
import com.extracme.evcard.mmp.service.IMmpDiscountRuleService;
import com.extracme.evcard.mmp.service.IMmpOperationLogService;
import com.extracme.evcard.mmp.vo.AgencyInfoBvmVO;
import com.extracme.evcard.mmp.vo.MemberShipAccountVO;
import com.extracme.evcard.mmp.vo.SearchAdminVO;
import com.extracme.evcard.mmp.vo.SearchLogVO;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.sts.rpc.service.IBillingConfigurationService;
import com.extracme.evcard.sts.rpc.service.dto.BillPackageTypeInfoDto;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：EnterpriseMemberController
 * 类描述：企业会员信息控制层
 * 创建人：林鑫
 * 创建时间：2018年5月15日下午3:00:00
 * 修改备注
 * @version1.0
 */
@RestController
@RequestMapping("api")
public class EnterpriseMemberController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMmpOperationLogService operationLogService;

    @Resource
    private IMmpDiscountRuleService mmpDiscountRuleService;

    @Resource
    public IAgencyService agencyService;

    @Resource
    private IMmpAgencyAccountService mmpAgencyAccountService;
    @Resource
    private IBillingConfigurationService billingConfigurationService;

    /**
     * 查询企业会员列表
     * @param agencyInfoSearchDTO
     * @return
     */
    @UserAuthRequired(resKey = "ItIxI4sWRneYpjbvBpOl5A")
    @RequestMapping(value = "queryAgencyMemberList", method = RequestMethod.POST)
    public DefaultWebRespVO queryAgencyMemberList(@RequestBody AgencyInfoSearchDTO agencyInfoSearchDTO){
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        if(agencyInfoSearchDTO.getPageSize() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示记录数");
        }else if(agencyInfoSearchDTO.getPageNum() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示页码数");
        }else if(agencyInfoSearchDTO.getIsAll() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入是否显示总条数");
        }else{
            if(agencyInfoSearchDTO.getPayWay() != null){
                agencyInfoSearchDTO.setSqlPayWay(agencyInfoSearchDTO.getPayWay().doubleValue());
            }
            PageBeanBO<AgencyInfoDTO> pageBO = agencyService.find(agencyInfoSearchDTO);
            respVO.setCode("1");
            respVO.setData(pageBO);
        }
        return respVO;
    }

    /**
     * 查询企业套餐类型列表
     * @return
     */
    @RequestMapping(value = "queryBillPackageTypeList", method = RequestMethod.GET)
    public DefaultWebRespVO queryBillPackageTypeList(){
        List<BillPackageTypeInfoDto> packageTypeList = billingConfigurationService.getBillPackageTypeInfo();
        return DefaultWebRespVO.getSuccessVO(packageTypeList);
    }


    /**
     * 新建企业会员
     * @param agencyInfoSaveDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "addAgencyMember", method = RequestMethod.POST)
    public DefaultWebRespVO addAgencyMember(@RequestBody AgencyInfoSaveDTO agencyInfoSaveDTO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        if(agencyInfoSaveDTO == null){
            respDTO.setCode(1);
            respDTO.setMessage("信息为空，请输入需要保存的企业会员信息");
        }else{
            respDTO = agencyService.save(agencyInfoSaveDTO,request);
        }
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 修改企业会员基础信息
     * @param agencyInfoUpdateDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "updateAgencyMember", method = RequestMethod.POST)
    public DefaultWebRespVO updateAgencyMember(@RequestBody AgencyInfoUpdateDTO agencyInfoUpdateDTO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = agencyService.update(agencyInfoUpdateDTO,request,null);
        log.info("修改企业会员");
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 获取企业会员详情
     * @param agencyId
     * @return
     */
    @RequestMapping(value = "getAgencyMember", method = RequestMethod.GET)
    public DefaultWebRespVO getAgencyMember(@RequestParam (value = "agencyId", required = true)String agencyId){
        AgencyInfoDeatailDTO agencyInfoDeatailDTO = agencyService.get(agencyId);
        log.info("获取企业会员详情");
        return DefaultWebRespVO.getSuccessVO(agencyInfoDeatailDTO);
    }

    @RequestMapping(value = "agencyInfoListenerFromLongShort", method = RequestMethod.POST)
    public DefaultWebRespVO getAgencyMember(@RequestBody AgencyInfoSyncInput input){
        BaseResponse baseResponse = agencyService.agencyInfoListenerFromLongShort(input);
        log.info("长租合同同步结束，input={},baseResponse={}", JSON.toJSONString(input), JSON.toJSON(baseResponse));
        return new DefaultWebRespVO(String.valueOf(baseResponse.getCode()), baseResponse.getMessage());
    }

    /**
     * 企业会员开始合作
     * @param agencyId
     * @param request
     * @return
     */
    @RequestMapping(value = "startCooperate", method = RequestMethod.PUT)
    public DefaultWebRespVO startCooperate(@RequestParam (value = "agencyId", required = true)String agencyId, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = agencyService.updateStartCooperate(agencyId,request, null);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 企业会员暂停合作
     * @param agencyId
     * @param request
     * @return
     */
    @RequestMapping(value = "pauseCooperate", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseCooperate(@RequestParam (value = "agencyId", required = true)String agencyId, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = agencyService.updatePauseCooperate(agencyId,request,null);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 企业会员新增折扣信息
     * @param discountRuleSaveDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "addDiscountRule", method = RequestMethod.POST)
    public DefaultWebRespVO addDiscountRule(@RequestBody DiscountRuleSaveDTO discountRuleSaveDTO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = mmpDiscountRuleService.save(discountRuleSaveDTO,request,null);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 获取企业会员折扣详情
     * @param agencyId
     * @param discountType
     * @return
     */
    @RequestMapping(value = "getDiscountDetail", method = RequestMethod.GET)
    public DefaultWebRespVO getDiscountDetail(@RequestParam (value = "agencyId", required = true)String agencyId,@RequestParam (value = "discountType", required = true)Integer discountType){
        DiscountRuleDTO discountRuleDTO = mmpDiscountRuleService.get(agencyId,discountType);
        if(discountRuleDTO != null){
            return  DefaultWebRespVO.getSuccessVO(discountRuleDTO);
        }else {
            return new DefaultWebRespVO("1","数据获取失败");
        }
    }

    /**
     * 企业会员操作日志
     * @param searchLogVO
     * @param request
     * @return
     */
    @RequestMapping(value = "operationLogList", method = RequestMethod.POST)
    public DefaultWebRespVO queryOperationLogList(@RequestBody SearchLogVO searchLogVO, HttpServletRequest request){
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        if(searchLogVO.getPageSize() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示记录数");
        }else if(searchLogVO.getPageNum() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示页码数");
        }else if(searchLogVO.getIsAll() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入是否显示总条数");
        }else{
            PageBeanBO<OperationLogDTO> pageBeanBO = operationLogService.find(searchLogVO.getPageNum(),searchLogVO.getPageSize(),searchLogVO.getAgencyId(),searchLogVO.getIsAll());
            respVO.setCode("0");
            respVO.setData(pageBeanBO);
        }
       return respVO;
    }

    /**
     * 企业会员操作日志——修改折扣详情
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "operationLogDetail", method = RequestMethod.GET)
    public DefaultWebRespVO getoperationLogDetail(@RequestParam(value = "id", required = true) Long id, HttpServletRequest request){
        OperationLogAgencyDetailDTO agencyDetailDTO = operationLogService.get(id);
        if(agencyDetailDTO != null){
            return DefaultWebRespVO.getSuccessVO(agencyDetailDTO);
        }else {
            return new DefaultWebRespVO("1","获取数据失败");
        }
    }

    /**
     * 获取超级管理员或管理员列表——调用企业用车平台
     * @param searchAdminVO
     * @return
     */
    @RequestMapping(value = "superAdministratorList", method = RequestMethod.POST)
    public DefaultWebRespVO superAdministratorList(@RequestBody SearchAdminVO searchAdminVO){
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        JSONObject result = new JSONObject();
        if(searchAdminVO.getPageSize() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示记录数");
        }else if(searchAdminVO.getPageNum() == null){
            respVO.setCode("1");
            respVO.setMessage("请输入显示页码数");
        }else if(searchAdminVO.getIsAll() == null){
            respVO.setMessage("请输入是否显示总条数");
        }else{
            if(1 == searchAdminVO.getAccountType()){
                /** 获取超级管理员列表*/
                result = mmpAgencyAccountService.findSuperAdminAcountInfo(searchAdminVO.getAgencyId(),searchAdminVO.getAccountType(),searchAdminVO.getPageNum(),searchAdminVO.getPageSize(),searchAdminVO.getIsAll());
            }else if(2 == searchAdminVO.getAccountType()){
                /** 获取管理员列表*/
                result = mmpAgencyAccountService.findAdminAcountInfo(searchAdminVO.getAgencyId(),searchAdminVO.getPageNum(),searchAdminVO.getPageSize(),searchAdminVO.getIsAll());
            }
        }
        if(result == null){
            respVO.setCode("1");
            respVO.setMessage("调用企业用车平台接口报错");
        }else if("0".equals(result.get("code"))){
            respVO.setCode("0");
            respVO.setData(result.get("data"));
        }else{
            respVO.setCode("1");
            respVO.setMessage(result.get("message").toString());
        }
        return respVO;
    }

    /**
     * 获取超级管理员详情——调用企业用车平台
     * @param id
     * @return
     */
    @RequestMapping(value = "getSuperAdministrator", method = RequestMethod.GET)
    public DefaultWebRespVO getSuperAdministrator(@RequestParam(value = "id", required = true) Long id){
        JSONObject result = mmpAgencyAccountService.getSuperAdminAcountInfo(id);
        if(result == null){
            return DefaultWebRespVO.getSuccessVO(result);
        }else {
            return new DefaultWebRespVO("1",result.get("message").toString());
        }
    }

    /**
     * 新建超级管理员账号——调用企业用车平台
     * @param agencyAccountSaveDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "saveSuperAdministrator", method = RequestMethod.POST)
    public DefaultWebRespVO saveSuperAdministrator(@RequestBody AgencyAccountSaveDTO agencyAccountSaveDTO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = mmpAgencyAccountService.addSuperAdminAcountInfo(agencyAccountSaveDTO,request,null);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 修改超级管理员详情——调用企业用车平台
     * @param agencyAccountUpdateDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "updateSuperAdministrator", method = RequestMethod.POST)
    public DefaultWebRespVO updateSuperAdministrator(@RequestBody AgencyAccountUpdateDTO agencyAccountUpdateDTO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = mmpAgencyAccountService.updateSuperAdminAcountInfo(agencyAccountUpdateDTO,request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 删除超级管理员——调用企业用车平台
     * @param accountInfDetailDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "deleteSuperAdministrator", method = RequestMethod.POST)
    public DefaultWebRespVO deleteSuperAdministrator(@RequestBody AgencyAccountInfDetailDTO accountInfDetailDTO, HttpServletRequest request){
        if(accountInfDetailDTO == null){
            return new DefaultWebRespVO("1","传入参数为空");
        }
        DefaultServiceRespDTO respDTO = mmpAgencyAccountService.deleteSuperAdminAcountInfo(accountInfDetailDTO,request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 禁用超级管理员——调用企业用车平台
     * @param data
     * @param request
     * @return
     */
    @RequestMapping(value = "forbiddenSuperAdministrator", method = RequestMethod.POST)
    public DefaultWebRespVO forbiddenSuperAdministrator(@RequestBody Accounts data, HttpServletRequest request){
        if(data == null){
            return new DefaultWebRespVO("1","传入参数为空");
        }
        DefaultServiceRespDTO respDTO = mmpAgencyAccountService.updateForbiddenSuperAdminAcountInfo(data.getData(),request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 恢复超级管理员——调用企业用车平台
     * @param data
     * @param request
     * @return
     */
    @RequestMapping(value = "recoverSuperAdministrator", method = RequestMethod.POST)
    public DefaultWebRespVO recoverSuperAdministrator(@RequestBody Accounts data, HttpServletRequest request){
        if(data == null){
            return new DefaultWebRespVO("1","传入参数为空");
        }
        DefaultServiceRespDTO respDTO = mmpAgencyAccountService.updateRecoverSuperAdministrator(data.getData(),request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("1",respDTO.getMessage());
        }
    }

    /**
     * 获取企业会员账号情况——部分参数调用企业用车平台
     * @param agencyId
     * @return
     */
    @RequestMapping(value = "getAgencyAccountInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getAgencyAccountInfo(@RequestParam(value = "agencyId", required = true) String agencyId){
        AgencyAccountInfoDTO accountInfoDTO = mmpAgencyAccountService.getAgencyAccountInfo(agencyId);
        if(accountInfoDTO == null){
            return  new DefaultWebRespVO("1","获取数据失败");
        }else {
            return DefaultWebRespVO.getSuccessVO(accountInfoDTO);
        }
    }

    /**
     * 提供接口给企业用车平台调用获取企业会员详情
     * @param agencyInfoBvmVO
     * @return
     */
    @RequestMapping(value = "getBvmAgencyInfo", method = RequestMethod.POST)
    public DefaultWebRespVO getBvmAgencyInfo(@RequestBody AgencyInfoBvmVO agencyInfoBvmVO){
        if(agencyInfoBvmVO != null){
            BvmAgencyInfoDTO bvmAgencyInfoDTO = agencyService.getBvmAgencyInfo(agencyInfoBvmVO.getAgencyId());
            if(bvmAgencyInfoDTO != null){
                return  DefaultWebRespVO.getSuccessVO(bvmAgencyInfoDTO);
            }else{
                return  new DefaultWebRespVO("1","获取数据失败");
            }
        }
        return  new DefaultWebRespVO("1","未传入agencyId");
    }
    
    /**
	 * 赠送E币- 企业
	 * 
	 * @param memberShipAccountVO
	 * @param agencyId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "presentEAmountForOrg/{agencyId}", method = RequestMethod.PUT)
	public DefaultWebRespVO presentEAmountForOrg(@RequestBody MemberShipAccountVO memberShipAccountVO,
			@PathVariable String agencyId, HttpServletRequest request) {
		MemberShipAccountDTO memberShipAccountDTO = new MemberShipAccountDTO();
		BeanCopyUtils.copyProperties(memberShipAccountVO, memberShipAccountDTO);
		DefaultServiceRespDTO respDTO = agencyService.presentEAmountForOrg(memberShipAccountDTO,
				agencyId, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("赠送E币- 企业");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "赠送成功");
	}

	/**
	 * 扣除赠送E币-企业
	 * 
	 * @param memberShipAccountVO
	 * @param agencyId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deductPresentEAmountForOrg/{agencyId}", method = RequestMethod.PUT)
	public DefaultWebRespVO deductPresentEAmountForOrg(@RequestBody MemberShipAccountVO memberShipAccountVO,
			@PathVariable String agencyId, HttpServletRequest request) {
		MemberShipAccountDTO memberShipAccountDTO = new MemberShipAccountDTO();
		BeanCopyUtils.copyProperties(memberShipAccountVO, memberShipAccountDTO);
		DefaultServiceRespDTO respDTO = agencyService.deductPresentEAmountForOrg(memberShipAccountDTO,
				agencyId, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("扣除赠送E币-企业");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "扣除成功");
	}
	
	/**
	 * 获取员工列表
	 * @param pageNum
	 * @param pageSize
	 * @param agencyId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAgencyMemberList", method = RequestMethod.GET)
	public DefaultWebRespVO getAgencyMemberList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "agencyId", required = false) String agencyId, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			PageBeanBO<AgencyMemberDTO> pageBeanBO = agencyService.getAgencyMemberList(agencyId, pageNum, pageSize,isAll);
			if (pageBeanBO != null) {
				return DefaultWebRespVO.getSuccessVO(pageBeanBO);
			} else {
				return new DefaultWebRespVO("1", "获取数据失败");
			}
		} catch (Exception e) {
			log.error("getAgencyMemberList 失败。", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("获取员工列表失败");
			return vo;
		}
	}
	
	/**
	 * 用车规则
	 * @param agencyRoleId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAgencyRoleSpec", method = RequestMethod.GET)
	public DefaultWebRespVO getAgencyRoleSpec(
			@RequestParam(value = "agencyRoleId", required = false) String agencyRoleId, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			AgencyRoleSpecDTO agencyMemberDTO = agencyService.getAgencyRoleSpec(agencyRoleId);
			if(agencyMemberDTO == null) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "用车规则不存在");
			}
			return DefaultWebRespVO.getSuccessVO(agencyMemberDTO);
		} catch (Exception e) {
			log.error("getAgencyRoleSpec 失败。", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("用车规则失败");
			return vo;
		}
	}


    /**
     * 新增企业与二级渠道关联关系
     */
    @RequestMapping(value = "addAgencySecondAppKeyRelation", method = RequestMethod.POST)
    public DefaultWebRespVO addAgencySecondAppKeyRelation(@RequestBody AddAgencySecondAppKeyRelationInput input,
                                                          HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = agencyService.addAgencySecondAppKeyRelation(input, request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("-1",respDTO.getMessage());
        }
    }

    /**
     * 删除企业与二级渠道关联关系
     */
    @RequestMapping(value = "deleteAgencySecondAppKeyRelation", method = RequestMethod.POST)
    public DefaultWebRespVO deleteAgencySecondAppKeyRelation(@RequestBody DeleteAgencySecondAppKeyRelationInput input,
                                                             HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = agencyService.deleteAgencySecondAppKeyRelation(input.getAgencyId(), request);
        if(respDTO.getCode() == 0){
            return DefaultWebRespVO.SUCCESS;
        }else {
            return new DefaultWebRespVO("-1",respDTO.getMessage());
        }
    }

    /**
     * 查询企业与二级渠道关联关系列表
     */
    @RequestMapping(value = "queryAgencySecondAppKeyRelation", method = RequestMethod.GET)
    public DefaultWebRespVO queryAgencySecondAppKeyRelation(@RequestParam String agencyId) {
        return agencyService.queryAgencySecondAppKeyRelation(agencyId);
    }

    /**
     * 查询企业折扣码
     * 检查企业渠道下单码生成状态，如果已生成则返回图片URL，如果未生成则先生成后返回
     */
    @RequestMapping(value = "queryAgencyDiscountCode", method = {RequestMethod.GET, RequestMethod.POST})
    public DefaultWebRespVO queryAgencyDiscountCode(@RequestParam String agencyId) {
        try {
            BaseResponse<String> response = agencyService.queryAgencyDiscountCode(agencyId);

            if (response.getCode() == 0) {
                return DefaultWebRespVO.getSuccessVO(response.getData(), response.getMessage());
            } else {
                return new DefaultWebRespVO(String.valueOf(response.getCode()), response.getMessage());
            }
        } catch (Exception e) {
            log.error("查询企业折扣码异常，agencyId={}", agencyId, e);
            return new DefaultWebRespVO("-1", "查询企业折扣码失败：" + e.getMessage());
        }
    }



}
