package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.mmp.service.IAgencyService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 长租会员同步控制器
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class LongRentMemberSyncController {

    @Resource
    private IAgencyService agencyService;

    /**
     * 长租会员同步接口
     * 
     * @param input 长租会员同步输入参数
     * @return 同步结果
     */
    @RequestMapping(value = "/longRentMemberSync", method = RequestMethod.POST)
    public DefaultWebRespVO longRentMemberSync(@RequestBody LongRentMemberSyncInput input) {
        log.info("接收到长租会员同步请求，企业名称：{}", input != null ? input.getAgencyName() : "null");
        
        try {
            BaseResponse<String> response = agencyService.longRentMemberSync(input);

            if (response.getCode() == 0) {
                // 返回 agencyId
                return DefaultWebRespVO.getSuccessVO(response.getData(), response.getMessage());
            } else {
                return new DefaultWebRespVO(String.valueOf(response.getCode()), response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("长租会员同步接口异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "长租会员同步失败：" + e.getMessage());
        }
    }
}
