# 长租会员同步功能字段映射实现说明

## 概述

根据字段映射表，已完成长租会员同步功能的输入参数调整和业务逻辑更新，确保所有固定值都正确设置。

## 输入参数调整

### LongRentMemberSyncInput 类更新

**简化后的输入参数：**
- `agencyName` - 企业名称（客户名称）- 必填
- `contactName` - 联系人姓名（客户主联系人姓名）- 必填  
- `contactMobile` - 联系人手机号（客户主联系人手机号）- 必填
- `contactEmail` - 联系人邮箱（客户主联系人邮箱）
- `licenseNo` - 企业营业执照号（客户营业证件号）- 非必填
- `referrerName` - 推荐人姓名（客户所属销售姓名）
- `referrerMobile` - 推荐人手机号（客户所属销售手机号）
- `customerId` - 客户ID（用作备注）
- `operatorId` - 操作人ID
- `operatorName` - 操作人姓名

## 固定值映射实现

### 企业会员信息固定值

| 字段 | 固定值 | 实现位置 |
|------|--------|----------|
| 企业性质 | "0"（外部） | `buildAgencyInfo()` |
| 结算方式 | 1.0（预付费） | `buildAgencyInfo()` |
| 订单支付方 | 2（个人支付） | `buildAgencyInfo()` |
| 押金担保方 | 2（个人支付） | `buildAgencyInfo()` |
| 违约承担方 | 2（个人承担） | `buildAgencyInfo()` |
| 业务来源 | 1（政企客户） | `buildAgencyInfo()` |
| 企业创建方式 | 3（长租客户） | `buildAgencyInfo()` |
| 失效时间 | 当前日期+3年 | `buildAgencyInfo()` |
| 月额度限制 | null（不限制） | 默认不设置 |
| 车牌限制 | ""（不限制） | `buildAgencyInfo()` |
| 用车阈值 | null（不限制） | 默认不设置 |
| 企业免押金额 | 0 | `buildAgencyInfo()` |

### 折扣规则固定值

| 字段 | 固定值 | 实现位置 |
|------|--------|----------|
| 个人通用折扣 | 95 | `createNewAgency()` |
| 个人旺季折扣 | 95 | `createNewAgency()` |
| 受益人数 | 50000 | `createNewAgency()` |
| 有效期区间 | 当前日期+3年 | `buildAgencyInfo()` |

### 超级管理员信息映射

| 字段 | 取值来源 | 实现位置 |
|------|---------|----------|
| 超级管理员手机 | 客户主联系人手机号 | `buildAgencyInfoFromInput()` |
| 超级管理员姓名 | 客户主联系人姓名 | `buildAgencyInfoFromInput()` |
| 超级管理员邮箱 | 客户主联系人邮箱 | `buildAgencyInfoFromInput()` |

## 业务逻辑更新

### 1. 参数校验调整
- 移除了企业营业执照号的必填校验（改为非必填）
- 保留企业名称、联系人姓名、联系人手机号的必填校验

### 2. 企业信息构建
- 应用所有固定值设置
- 自动计算失效时间（当前日期+3年）
- 设置备注为客户ID

### 3. 折扣规则初始化
- 个人折扣率固定为95%
- 受益人数固定为50000

### 4. 返回值优化
- 确保同步成功时返回正确的 agencyId

## API 接口

### 请求示例

```json
{
  "agencyName": "测试企业名称",
  "contactName": "张三",
  "contactMobile": "13800138000",
  "contactEmail": "<EMAIL>",
  "licenseNo": "91110000000000000X",
  "referrerName": "销售经理",
  "referrerMobile": "13900139000",
  "customerId": "CUST001",
  "operatorId": 1,
  "operatorName": "系统管理员"
}
```

### 响应示例

```json
{
  "code": "0",
  "message": "企业会员创建成功",
  "data": "AGENCY001"
}
```

## 实现细节

### 1. 固定值设置位置

**在 `buildAgencyInfo()` 方法中设置：**
```java
// 设置固定值（根据字段映射表）
agencyInfo.setOrgProperty("0"); // 企业性质：0（外部）
agencyInfo.setPayWay(1.0); // 结算方式：1.0（预付费）
agencyInfo.setOrderPayer(2); // 订单支付方：2（个人支付）
agencyInfo.setDepositGuarantor(2); // 押金担保方：2（个人支付）
agencyInfo.setDefaultingParty(2); // 违约承担方：2（个人承担）
agencyInfo.setBusinessSource(1); // 业务来源：1（政企客户）
agencyInfo.setCreateWay(3); // 创建方式：3（长租客户）
agencyInfo.setVehicleNo(""); // 车牌限制：""（不限制）
agencyInfo.setExemptDepositAmount(new BigDecimal("0")); // 企业免押金额：0
```

**在 `createNewAgency()` 方法中设置：**
```java
// 个人通用折扣和旺季折扣：95
Double discountRate = 95.0;
// 受益人数：50000
Integer beneficiaryNumber = 50000;
```

### 2. 时间计算
```java
// 设置失效时间：当前日期+3年
Date now = new Date();
Calendar calendar = Calendar.getInstance();
calendar.setTime(now);
calendar.add(Calendar.YEAR, 3);
agencyInfo.setCooperateStartTime(now);
agencyInfo.setCooperateEndTime(calendar.getTime());
```

### 3. 返回值处理
```java
// 在处理器中确保返回 agencyId
return new BaseResponse(0, "企业会员创建成功", agencyId);

// 在控制器中确保正确返回
String agencyId = response.getData() != null ? response.getData().toString() : null;
return DefaultWebRespVO.getSuccessVO(agencyId, response.getMessage());
```

## 验证清单

- ✅ 输入参数简化完成
- ✅ 所有固定值正确设置
- ✅ 折扣规则按规范配置
- ✅ 时间计算正确实现
- ✅ 返回值包含 agencyId
- ✅ 参数校验调整完成
- ✅ 代码编译无错误
- ✅ 测试文件已删除
- ✅ 文档文件已删除

## 注意事项

1. **企业营业执照号**：已改为非必填，如果客户没有可以不传
2. **固定值**：所有固定值都按照字段映射表严格设置，不可修改
3. **时间设置**：失效时间自动设置为当前日期+3年
4. **返回值**：同步成功时必须返回 agencyId 供后续使用
5. **备注字段**：使用客户ID作为备注，便于追溯

## 总结

本次更新完全按照字段映射表的要求实现，简化了输入参数，固化了业务规则，确保了数据的一致性和规范性。所有固定值都已正确设置，同步成功时会返回正确的 agencyId。
