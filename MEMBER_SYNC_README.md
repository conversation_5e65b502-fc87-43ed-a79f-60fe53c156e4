# 会员同步逻辑功能

## 概述

本功能实现了基于客户名称判断企业会员状态，并执行相应的创建或更新操作的会员同步逻辑。参考了现有的 `IAgencyService#agencyInfoListenerFromLongShort` 方法的实现模式。

## 功能特性

### 核心功能
- 根据客户名称判断企业会员状态
- 执行相应的创建或更新操作
- 自动创建和更新默认用车规则
- 完整的异常处理和日志记录
- 易于维护和扩展的代码结构

### 业务场景支持

#### 场景A：企业会员不存在
- 基于长租客户信息创建新的企业会员
- 同时创建默认用车规则

#### 场景B：企业会员存在，但用车规则不存在
根据企业创建方式执行不同逻辑：

1. **政企框架合同创建的企业：**
   - 合作状态 ≠ "暂停中"：不更新信息，返回"企业会员已创建，信息无法更新"
   - 合作状态 = "暂停中"：更新企业信息（推荐人信息保持不变），返回"企业会员已创建，信息已更新"

2. **会员系统创建的企业：**
   - 合作状态 ≠ "暂停中"：不更新信息，返回"企业会员已创建，信息无法更新"
   - 合作状态 = "暂停中"：更新企业信息（推荐人信息保持不变），返回"企业会员已创建，信息已更新"

3. **长租客户创建的企业：**
   - 直接更新企业信息（推荐人信息保持不变），返回"企业会员已创建，信息已更新"

- 所有情况下都需要创建默认用车规则

#### 场景C：企业会员存在，且用车规则也存在
根据企业创建方式执行不同逻辑（同场景B），但额外需要将用车规则更新为默认规则。

## 技术架构

### 核心组件

1. **IMemberSyncService** - 会员同步服务接口
2. **MemberSyncServiceImpl** - 会员同步服务实现类
3. **MemberSyncProcessor** - 会员同步业务逻辑处理器
4. **IVehicleRuleService** - 用车规则服务接口
5. **VehicleRuleServiceImpl** - 用车规则服务实现类
6. **MemberSyncController** - REST 控制器

### 数据传输对象

- **MemberSyncInput** - 会员同步输入参数

### 设计模式

- **策略模式**：通过 MemberSyncProcessor 处理不同业务场景
- **模板方法模式**：在处理器中定义算法骨架，具体实现在服务类中
- **依赖注入**：通过 Spring 框架管理组件依赖关系

## API 接口

### 会员同步接口

**URL:** `POST /api/memberSync`

**请求参数:**
```json
{
  "agencyName": "企业名称",
  "contactName": "联系人姓名",
  "contactMobile": "联系人手机号",
  "licenseNo": "企业营业执照号",
  "tel": "企业座机",
  "email": "企业邮箱",
  "fax": "企业传真",
  "address": "企业地址",
  "remark": "备注",
  "cooperateStartTime": "2024-01-01",
  "cooperateEndTime": "2024-12-31",
  "cooperateStatus": 1,
  "maxCarNumber": 10,
  "discountRate": 95.0,
  "beneficiaryNumber": 50,
  "exemptDepositAmount": 1000,
  "depositGuarantor": 1,
  "orderPayer": 1,
  "defaultingParty": 1,
  "businessSource": 4,
  "createWay": 3,
  "referrerName": "推荐人姓名",
  "referrerMobile": "推荐人手机号",
  "longRentContractId": "长租合同编号",
  "orgProperty": "0",
  "payWay": 1.0,
  "lineLimitMonthly": 10000.0,
  "insideFlag": 0,
  "vehicleNo": "",
  "maxUnitPrice": 100,
  "operatorId": 1,
  "operatorName": "操作员姓名"
}
```

**响应格式:**
```json
{
  "code": "0",
  "message": "企业会员创建成功",
  "data": "AGENCY001"
}
```

## 配置说明

### BVM 系统接口配置

在 `application-local.properties` 中配置 BVM 系统的用车规则管理接口：

```properties
# 用车规则管理
url.createDefaultAgencyRole = http://csms-st.evcard.vip/evcard-bvm/inner/createDefaultAgencyRole
url.updateAgencyRoleToDefault = http://csms-st.evcard.vip/evcard-bvm/inner/updateAgencyRoleToDefault
url.getAgencyRoleList = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyRoleList
url.getDefaultAgencyRole = http://csms-st.evcard.vip/evcard-bvm/inner/getDefaultAgencyRole
```

### 默认用车规则配置

系统会自动创建以下默认用车规则：
- 免押金等级：0
- 企业支付：否
- 个人支付：是
- 个人限制：否
- 限制用车时间段：否
- 限制取车网点：否
- 限制还车网点：否
- 限制车型价格：否
- 限制单笔订单金额上限：否

## 使用示例

### Java 代码调用

```java
@Resource
private IAgencyService agencyService;

public void syncMemberExample() {
    MemberSyncInput input = new MemberSyncInput();
    input.setAgencyName("示例企业");
    input.setContactName("张三");
    input.setContactMobile("13800138000");
    input.setLicenseNo("91110000000000000X");
    // ... 设置其他必要参数
    
    BaseResponse response = agencyService.memberSync(input);
    
    if (response.getCode() == 0) {
        System.out.println("同步成功：" + response.getMessage());
    } else {
        System.out.println("同步失败：" + response.getMessage());
    }
}
```

### HTTP 请求示例

```bash
curl -X POST http://localhost:8080/api/memberSync \
  -H "Content-Type: application/json" \
  -d '{
    "agencyName": "示例企业",
    "contactName": "张三",
    "contactMobile": "13800138000",
    "licenseNo": "91110000000000000X",
    "cooperateStatus": 1,
    "maxCarNumber": 10,
    "createWay": 3,
    "operatorId": 1,
    "operatorName": "操作员"
  }'
```

## 测试

### 单元测试

项目包含完整的单元测试：
- `MemberSyncServiceImplTest` - 服务层测试
- `VehicleRuleServiceImplTest` - 用车规则服务测试
- `MemberSyncControllerTest` - 控制器测试

### 集成测试

- `MemberSyncIntegrationTest` - 完整流程集成测试

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=MemberSyncServiceImplTest

# 运行集成测试（需要数据库环境）
mvn test -Dtest=MemberSyncIntegrationTest
```

## 错误处理

### 常见错误码

- `0`: 成功
- `-1`: 失败（具体错误信息在 message 中）

### 常见错误信息

- "企业名称不能为空"
- "联系人姓名不能为空"
- "联系人手机号不能为空"
- "企业营业执照号不能为空"
- "创建企业会员失败"
- "创建默认用车规则失败"

## 日志记录

系统会记录详细的操作日志，包括：
- 输入参数
- 业务场景判断
- 操作结果
- 异常信息

日志级别：
- INFO：正常业务流程
- WARN：警告信息（如用车规则创建失败但不影响主流程）
- ERROR：错误信息

## 扩展说明

### 添加新的业务场景

1. 在 `MemberSyncProcessor` 中添加新的处理方法
2. 在 `MemberSyncServiceImpl` 中实现具体逻辑
3. 添加相应的单元测试

### 修改默认用车规则

在 `VehicleRuleServiceImpl.buildDefaultVehicleRuleParams()` 方法中修改默认参数。

### 集成其他系统

通过修改 `VehicleRuleServiceImpl` 中的 HTTP 调用逻辑，可以集成其他用车规则管理系统。

## 注意事项

1. **事务管理**：会员同步操作使用了 `@Transactional` 注解，确保数据一致性
2. **推荐人信息保护**：在更新企业信息时，会保持原有的推荐人信息不变
3. **姓名长度限制**：联系人姓名超过20个字符时会自动截取
4. **BVM 接口依赖**：如果 BVM 接口不可用，系统会使用模拟模式继续运行
5. **并发安全**：服务类是无状态的，支持并发调用

## 维护建议

1. 定期检查 BVM 接口的可用性
2. 监控会员同步的成功率和响应时间
3. 定期清理测试数据
4. 关注日志中的警告信息，及时处理潜在问题
