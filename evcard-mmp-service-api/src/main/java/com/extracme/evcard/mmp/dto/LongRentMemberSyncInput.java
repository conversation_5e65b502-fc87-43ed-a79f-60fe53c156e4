package com.extracme.evcard.mmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 长租会员同步输入参数
 * 字段命名与 AgencyInfoSyncInput 保持一致
 */
@Data
public class LongRentMemberSyncInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业名称（客户名称）- 必填
     */
    private String agencyName;

    /**
     * 失效时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Byte createWay;

    /**
     * 长租框架合同编号
     */
    private String longRentContractId;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Byte depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Byte orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Byte defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店 4=长租客户
     */
    private Byte businessSource;

    /**
     * 联系人姓名（客户主联系人姓名）- 必填
     */
    private String contractsName;

    /**
     * 联系人手机号（客户主联系人手机号）- 必填
     */
    private String contractsMobile;

    /**
     * 客户营业证件号 - 非必填
     */
    private String licenseNo;

    /**
     * 用车阈值（最大在租车辆数）
     */
    private Integer maxCarNumber;

    /**
     * 合同折扣
     */
    private Double contractDiscount;

    /**
     * 受益人数
     */
    private Integer beneficiaryNumber;

    /**
     * 订单免押额度
     */
    private BigDecimal exemptDepositAmount;

    /**
     * 企业座机
     */
    private String tel;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 企业传真
     */
    private String fax;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 备注（客户ID）
     */
    private String remark;

    /**
     * 合作开始时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date cooperateStartTime;

    /**
     * 合作结束时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date cooperateEndTime;

    /**
     * 合作状态 0-未开始；1-合作中；2-已暂停
     */
    private Integer cooperateStatus;

    /**
     * 企业折扣率
     */
    private Double discountRate;

    /**
     * 企业性质：0（外部）1（内部）
     */
    private String orgProperty;

    /**
     * 结算方式
     */
    private Double payWay;

    /**
     * 每月可透支额度
     */
    private Double lineLimitMonthly;

    /**
     * 内循环用车标识
     */
    private Integer insideFlag;

    /**
     * 车牌限制
     */
    private String vehicleNo;

    /**
     * 最大单价限制
     */
    private BigDecimal maxUnitPrice;

    /**
     * 推荐人姓名（客户所属销售姓名）
     */
    private String referrerName;

    /**
     * 推荐人手机号（客户所属销售手机号）
     */
    private String referrerMobile;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
