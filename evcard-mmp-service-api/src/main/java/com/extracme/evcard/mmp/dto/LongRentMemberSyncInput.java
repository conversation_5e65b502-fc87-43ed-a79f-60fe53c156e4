package com.extracme.evcard.mmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 长租会员同步输入参数
 */
@Data
public class LongRentMemberSyncInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业名称（客户名称）- 必填
     */
    private String agencyName;

    /**
     * 联系人姓名（客户主联系人姓名）- 必填
     */
    private String contactName;

    /**
     * 联系人手机号（客户主联系人手机号）- 必填
     */
    private String contactMobile;

    /**
     * 联系人邮箱（客户主联系人邮箱）
     */
    private String contactEmail;

    /**
     * 企业营业执照号（客户营业证件号）- 非必填
     */
    private String licenseNo;

    /**
     * 推荐人姓名（客户所属销售姓名）
     */
    private String referrerName;

    /**
     * 推荐人手机号（客户所属销售手机号）
     */
    private String referrerMobile;

    /**
     * 客户ID（用作备注）
     */
    private String customerId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
