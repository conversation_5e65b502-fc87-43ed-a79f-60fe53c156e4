package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * 长租会员同步服务接口
 */
public interface ILongRentMemberSyncService {

    /**
     * 长租会员同步逻辑
     * 根据客户名称判断企业会员状态，并执行相应的创建或更新操作
     * 
     * @param input 长租会员同步输入参数
     * @return 同步结果
     */
    BaseResponse syncLongRentMember(LongRentMemberSyncInput input);
}
