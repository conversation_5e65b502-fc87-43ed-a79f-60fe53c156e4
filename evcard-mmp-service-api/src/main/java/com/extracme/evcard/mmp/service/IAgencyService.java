package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.bo.QueryExportInfoBO;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

public interface IAgencyService {
    /**
     * 关联企业下拉框
     *
     * @param request
     * @return
     */
    List<AgencyDTO> getAgencyAll(HttpServletRequest request);

    /**
     * 渠道来源下拉框
     *
     * @param request
     * @return
     */
    List<AppDTO> getAppAll(AppDTO appDTO, HttpServletRequest request);

    /**
     * 会员所属公司下拉框
     *
     * @param request
     * @return
     */
    List<OrgInfoDTO> findAll(HttpServletRequest request);

    /**
     * 会员所属公司下拉框
     *
     * @param request
     * @return
     */
    List<OrgInfoDTO> findAllOrgs(HttpServletRequest request);

    List<OrgInfoDTO> queryPlateInfoList(String orgId);

    List<ActivityNameDTO> queryNightCarActivityList(HttpServletRequest request);

    /**
     * 免押等级下拉框
     *
     * @param request
     * @return
     */
    List<MmpDepositGradeDTO> queryExemptDepositGradeList(HttpServletRequest request);

    /**
     * 优惠券发放记录一览活动名称下拉框
     *
     * @param request
     * @return
     */
    List<String> queryActivityNameList(HttpServletRequest request);

    /**
     * 根据会员名称查询会员手机号
     * @param name 会员名称
     * @return
     */
    List<String> queryTelByName(String name);

    /**
     * 根据会员手机号查询会员名称
     *
     * @param mobilePhone 会员手机号
     * @return
     */
    List<String> queryNameByTel(String mobilePhone);

    /**
     * 根据活动编号查询活动名称、类型、所属单位
     *
     * @param actionId 活动编号
     * @return
     */
    ActivityInfoDTO queryActivityInfoById(String actionId);

    /**
     * 根据活动名称、类型、所属单位查询活动编号
     *
     * @param activityName 活动编号
     * @param type         活动类型
     * @param orgId        所属单位
     * @return
     */
    List<String> queryActivityId(String activityName, Integer type, String orgId);

    /**
     * 根据活动名称、类型、所属单位查询活动编号和活动名称
     *
     * @param activityName 活动编号
     * @param type         活动类型
     * @param orgId        所属单位
     * @return
     */
    List<ActivityIdAndNameDTO> queryActivityIdAndName(String activityName, Integer type, String orgId);

    /**
     * 企业会员管理列表
     * @param agencyInfoSearchDTO
     * @return
     */
    PageBeanBO<AgencyInfoDTO> find(AgencyInfoSearchDTO agencyInfoSearchDTO);

    /**
     * 新建企业会员信息
     * @param agencyInfoSaveDTO
     * @param request
     * @return
     */
    DefaultServiceRespDTO save(AgencyInfoSaveDTO agencyInfoSaveDTO, HttpServletRequest request);

    /**
     * 修改企业会员信息
     * @param agencyInfoUpdateDTO
     * @param request
     * @return
     */
    DefaultServiceRespDTO update(AgencyInfoUpdateDTO agencyInfoUpdateDTO, HttpServletRequest request, OperatorDTO operatorDTO);

    /**
     * 获取企业信息
     * @param agencyId
     * @return
     */
    AgencyInfoDeatailDTO get(String agencyId);

    /**
     * 企业会员开始合作
     * @param agencyId
     * @param request
     * @return
     */
    DefaultServiceRespDTO updateStartCooperate(String agencyId,HttpServletRequest request, OperatorDTO operatorDTO);

    /**
     * 企业会员暂停合作
     * @param agencyId
     * @return
     */
    DefaultServiceRespDTO updatePauseCooperate(String agencyId,HttpServletRequest request, OperatorDTO operatorDTO);

    /**
     * 企业用车平台调用，获取企业相关信息
     * @param agencyId
     * @return
     */
    BvmAgencyInfoDTO getBvmAgencyInfo(String agencyId);
    
    /**
	 * 赠送E币- 企业
     * @param agencyId 
	 */
	DefaultServiceRespDTO presentEAmountForOrg(MemberShipAccountDTO memberShipAccountDTO, String agencyId,
			HttpServletRequest request);

	/**
	 * 扣除赠送E币-企业
	 * @param memberShipAccountDTO
	 * @param agencyId
	 * @param request
	 * @return
	 */
	DefaultServiceRespDTO deductPresentEAmountForOrg(MemberShipAccountDTO memberShipAccountDTO, String agencyId,
			HttpServletRequest request);
	
	/**
	 * 获取员工列表
	 * @param agencyId
	 * @param pageNum
	 * @param pageSize
	 * @param isAll 
	 * @return
	 */
	PageBeanBO<AgencyMemberDTO> getAgencyMemberList(String agencyId, Integer pageNum, Integer pageSize, Integer isAll);

	/**
	 * 用车规则
	 * @param agencyRoleId
	 * @return
	 */
	AgencyRoleSpecDTO getAgencyRoleSpec(String agencyRoleId);

    /**
     * 长租合同同步
     * @param input
     * @return
     */
    public BaseResponse agencyInfoListenerFromLongShort(AgencyInfoSyncInput input);

    /**
     * 会员同步逻辑
     * @param input
     * @return
     */
    public BaseResponse memberSync(MemberSyncInput input);

    /**
     * 新增渠道下单码
     * @param input
     * @param request
     * @return
     */
    DefaultServiceRespDTO addAgencySecondAppKeyRelation(AddAgencySecondAppKeyRelationInput input, HttpServletRequest request);

    /**
     *  删除渠道下单码
     * @param agencyId
     * @param request
     * @return
     */
    DefaultServiceRespDTO deleteAgencySecondAppKeyRelation(String agencyId, HttpServletRequest request);

    /**
     * 查询渠道下单码
     * @param agencyId
     * @return
     */
    DefaultWebRespVO queryAgencySecondAppKeyRelation(String agencyId);
}
