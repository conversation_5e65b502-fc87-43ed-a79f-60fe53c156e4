package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.mmp.common.HttpUtils;
import com.extracme.evcard.mmp.common.PropertyUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * VehicleRuleServiceImpl 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class VehicleRuleServiceImplTest {

    private VehicleRuleServiceImpl vehicleRuleService;

    @Before
    public void setUp() {
        vehicleRuleService = new VehicleRuleServiceImpl();
    }

    @Test
    public void testCreateDefaultVehicleRule_Success() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.createDefaultAgencyRole"))
                    .thenReturn("http://test.com/createRole");
            
            // Mock HTTP调用成功响应
            JSONObject successResponse = new JSONObject();
            successResponse.put("code", "0");
            successResponse.put("message", "创建成功");
            successResponse.put("data", "RULE001");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(successResponse);

            BaseResponse result = vehicleRuleService.createDefaultVehicleRule("AGENCY001", 1L, "测试操作员");

            assertEquals(0, result.getCode());
            assertEquals("创建默认用车规则成功", result.getMessage());
            assertEquals("RULE001", result.getData());
        }
    }

    @Test
    public void testCreateDefaultVehicleRule_Failed() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.createDefaultAgencyRole"))
                    .thenReturn("http://test.com/createRole");
            
            // Mock HTTP调用失败响应
            JSONObject failResponse = new JSONObject();
            failResponse.put("code", "-1");
            failResponse.put("message", "创建失败");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(failResponse);

            BaseResponse result = vehicleRuleService.createDefaultVehicleRule("AGENCY001", 1L, "测试操作员");

            assertEquals(-1, result.getCode());
            assertTrue(result.getMessage().contains("创建默认用车规则失败"));
        }
    }

    @Test
    public void testCreateDefaultVehicleRule_NoUrlConfig() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class)) {
            
            // Mock配置为空
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.createDefaultAgencyRole"))
                    .thenReturn(null);

            BaseResponse result = vehicleRuleService.createDefaultVehicleRule("AGENCY001", 1L, "测试操作员");

            assertEquals(0, result.getCode());
            assertTrue(result.getMessage().contains("模拟创建默认用车规则成功"));
            assertTrue(result.getData().toString().startsWith("MOCK_RULE_"));
        }
    }

    @Test
    public void testCreateDefaultVehicleRule_EmptyAgencyId() {
        BaseResponse result = vehicleRuleService.createDefaultVehicleRule("", 1L, "测试操作员");

        assertEquals(-1, result.getCode());
        assertEquals("企业ID不能为空", result.getMessage());
    }

    @Test
    public void testCreateDefaultVehicleRule_NullAgencyId() {
        BaseResponse result = vehicleRuleService.createDefaultVehicleRule(null, 1L, "测试操作员");

        assertEquals(-1, result.getCode());
        assertEquals("企业ID不能为空", result.getMessage());
    }

    @Test
    public void testUpdateToDefaultVehicleRule_Success() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.updateAgencyRoleToDefault"))
                    .thenReturn("http://test.com/updateRole");
            
            // Mock HTTP调用成功响应
            JSONObject successResponse = new JSONObject();
            successResponse.put("code", "0");
            successResponse.put("message", "更新成功");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(successResponse);

            BaseResponse result = vehicleRuleService.updateToDefaultVehicleRule("AGENCY001", "RULE001", 1L, "测试操作员");

            assertEquals(0, result.getCode());
            assertEquals("更新用车规则为默认规则成功", result.getMessage());
        }
    }

    @Test
    public void testUpdateToDefaultVehicleRule_EmptyParams() {
        BaseResponse result1 = vehicleRuleService.updateToDefaultVehicleRule("", "RULE001", 1L, "测试操作员");
        assertEquals(-1, result1.getCode());
        assertEquals("企业ID和规则ID不能为空", result1.getMessage());

        BaseResponse result2 = vehicleRuleService.updateToDefaultVehicleRule("AGENCY001", "", 1L, "测试操作员");
        assertEquals(-1, result2.getCode());
        assertEquals("企业ID和规则ID不能为空", result2.getMessage());
    }

    @Test
    public void testHasVehicleRule_True() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.getAgencyRoleList"))
                    .thenReturn("http://test.com/getRoleList");
            
            // Mock HTTP调用成功响应，返回非空数据
            JSONObject successResponse = new JSONObject();
            successResponse.put("code", "0");
            successResponse.put("data", "[{\"id\":\"RULE001\",\"name\":\"默认规则\"}]");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(successResponse);

            boolean result = vehicleRuleService.hasVehicleRule("AGENCY001");

            assertTrue(result);
        }
    }

    @Test
    public void testHasVehicleRule_False() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.getAgencyRoleList"))
                    .thenReturn("http://test.com/getRoleList");
            
            // Mock HTTP调用成功响应，返回空数据
            JSONObject successResponse = new JSONObject();
            successResponse.put("code", "0");
            successResponse.put("data", "[]");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(successResponse);

            boolean result = vehicleRuleService.hasVehicleRule("AGENCY001");

            assertFalse(result);
        }
    }

    @Test
    public void testHasVehicleRule_EmptyAgencyId() {
        boolean result1 = vehicleRuleService.hasVehicleRule("");
        assertFalse(result1);

        boolean result2 = vehicleRuleService.hasVehicleRule(null);
        assertFalse(result2);
    }

    @Test
    public void testGetDefaultVehicleRuleId_Success() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.getDefaultAgencyRole"))
                    .thenReturn("http://test.com/getDefaultRole");
            
            // Mock HTTP调用成功响应
            JSONObject successResponse = new JSONObject();
            successResponse.put("code", "0");
            successResponse.put("data", "RULE001");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(successResponse);

            String result = vehicleRuleService.getDefaultVehicleRuleId("AGENCY001");

            assertEquals("RULE001", result);
        }
    }

    @Test
    public void testGetDefaultVehicleRuleId_NotFound() {
        try (MockedStatic<PropertyUtils> propertyUtilsMock = Mockito.mockStatic(PropertyUtils.class);
             MockedStatic<HttpUtils> httpUtilsMock = Mockito.mockStatic(HttpUtils.class)) {
            
            // Mock配置
            propertyUtilsMock.when(() -> PropertyUtils.getProperty("url.getDefaultAgencyRole"))
                    .thenReturn("http://test.com/getDefaultRole");
            
            // Mock HTTP调用失败响应
            JSONObject failResponse = new JSONObject();
            failResponse.put("code", "-1");
            failResponse.put("message", "未找到");
            
            httpUtilsMock.when(() -> HttpUtils.httpPostWithJSON(anyString(), any(Map.class)))
                    .thenReturn(failResponse);

            String result = vehicleRuleService.getDefaultVehicleRuleId("AGENCY001");

            assertNull(result);
        }
    }

    @Test
    public void testGetDefaultVehicleRuleId_EmptyAgencyId() {
        String result1 = vehicleRuleService.getDefaultVehicleRuleId("");
        assertNull(result1);

        String result2 = vehicleRuleService.getDefaultVehicleRuleId(null);
        assertNull(result2);
    }
}
