package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.rpc.dto.BaseResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * 长租会员同步集成测试
 * 
 * 注意：这个测试需要在有数据库连接的环境中运行
 * 如果没有数据库环境，可以跳过这个测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional // 确保测试后回滚数据
public class LongRentMemberSyncIntegrationTest {

    @Resource
    private ILongRentMemberSyncService longRentMemberSyncService;

    @Test
    public void testLongRentMemberSyncIntegration_ScenarioA() {
        // 集成测试场景A：企业会员不存在
        LongRentMemberSyncInput input = createTestInput("集成测试企业A_" + System.currentTimeMillis());
        
        BaseResponse result = longRentMemberSyncService.syncLongRentMember(input);
        
        // 验证结果
        assertEquals(0, result.getCode());
        assertTrue(result.getMessage().contains("企业会员创建成功") || 
                  result.getMessage().contains("企业会员已创建"));
    }

    @Test
    public void testLongRentMemberSyncIntegration_ScenarioB() {
        // 集成测试场景B：先创建企业，再测试用车规则不存在的情况
        String agencyName = "集成测试企业B_" + System.currentTimeMillis();
        LongRentMemberSyncInput input = createTestInput(agencyName);
        
        // 第一次调用：创建企业
        BaseResponse firstResult = longRentMemberSyncService.syncLongRentMember(input);
        assertEquals(0, firstResult.getCode());
        
        // 修改输入参数，模拟场景B
        input.setContactName("更新后的联系人");
        input.setContactMobile("13900139001");
        
        // 第二次调用：应该触发场景B或C的逻辑
        BaseResponse secondResult = longRentMemberSyncService.syncLongRentMember(input);
        assertEquals(0, secondResult.getCode());
        assertTrue(secondResult.getMessage().contains("企业会员已创建"));
    }

    @Test
    public void testLongRentMemberSyncIntegration_InvalidInput() {
        // 集成测试：无效输入
        LongRentMemberSyncInput input = new LongRentMemberSyncInput();
        // 故意不设置必要的字段
        
        BaseResponse result = longRentMemberSyncService.syncLongRentMember(input);
        
        assertEquals(-1, result.getCode());
        assertTrue(result.getMessage().contains("不能为空"));
    }

    @Test
    public void testLongRentMemberSyncIntegration_LongContactName() {
        // 集成测试：联系人姓名过长
        LongRentMemberSyncInput input = createTestInput("集成测试企业长姓名_" + System.currentTimeMillis());
        input.setContactName("这是一个非常非常非常非常非常长的联系人姓名，超过了20个字符的限制，应该被截取");
        
        BaseResponse result = longRentMemberSyncService.syncLongRentMember(input);
        
        assertEquals(0, result.getCode());
        // 验证姓名被正确截取
        assertEquals(20, input.getContactName().length());
    }

    @Test
    public void testLongRentMemberSyncIntegration_DifferentCreateWays() {
        // 集成测试：不同创建方式的企业
        String baseAgencyName = "集成测试企业创建方式_" + System.currentTimeMillis();
        
        // 测试会员系统创建
        LongRentMemberSyncInput input1 = createTestInput(baseAgencyName + "_会员系统");
        input1.setCreateWay(1);
        BaseResponse result1 = longRentMemberSyncService.syncLongRentMember(input1);
        assertEquals(0, result1.getCode());
        
        // 测试政企框架合同创建
        LongRentMemberSyncInput input2 = createTestInput(baseAgencyName + "_政企框架");
        input2.setCreateWay(2);
        BaseResponse result2 = longRentMemberSyncService.syncLongRentMember(input2);
        assertEquals(0, result2.getCode());
        
        // 测试长租客户创建
        LongRentMemberSyncInput input3 = createTestInput(baseAgencyName + "_长租客户");
        input3.setCreateWay(3);
        BaseResponse result3 = longRentMemberSyncService.syncLongRentMember(input3);
        assertEquals(0, result3.getCode());
    }

    @Test
    public void testLongRentMemberSyncIntegration_DifferentCooperateStatus() {
        // 集成测试：不同合作状态
        String baseAgencyName = "集成测试企业合作状态_" + System.currentTimeMillis();
        
        // 测试未开始状态
        LongRentMemberSyncInput input1 = createTestInput(baseAgencyName + "_未开始");
        input1.setCooperateStatus(0);
        BaseResponse result1 = longRentMemberSyncService.syncLongRentMember(input1);
        assertEquals(0, result1.getCode());
        
        // 测试合作中状态
        LongRentMemberSyncInput input2 = createTestInput(baseAgencyName + "_合作中");
        input2.setCooperateStatus(1);
        BaseResponse result2 = longRentMemberSyncService.syncLongRentMember(input2);
        assertEquals(0, result2.getCode());
        
        // 测试暂停中状态
        LongRentMemberSyncInput input3 = createTestInput(baseAgencyName + "_暂停中");
        input3.setCooperateStatus(2);
        BaseResponse result3 = longRentMemberSyncService.syncLongRentMember(input3);
        assertEquals(0, result3.getCode());
    }

    /**
     * 创建测试输入参数
     */
    private LongRentMemberSyncInput createTestInput(String agencyName) {
        LongRentMemberSyncInput input = new LongRentMemberSyncInput();
        input.setAgencyName(agencyName);
        input.setContactName("集成测试联系人");
        input.setContactMobile("13800138000");
        input.setLicenseNo("91110000000000000X");
        input.setTel("010-12345678");
        input.setEmail("<EMAIL>");
        input.setFax("010-87654321");
        input.setAddress("北京市朝阳区测试地址");
        input.setRemark("集成测试备注");
        input.setCooperateStartTime(new Date());
        input.setCooperateEndTime(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 一年后
        input.setCooperateStatus(1); // 合作中
        input.setMaxCarNumber(10);
        input.setDiscountRate(95.0);
        input.setBeneficiaryNumber(50);
        input.setExemptDepositAmount(new BigDecimal("1000"));
        input.setDepositGuarantor(1); // 企业支付
        input.setOrderPayer(1); // 企业支付
        input.setDefaultingParty(1); // 企业承担
        input.setBusinessSource(4); // 长租客户
        input.setCreateWay(3); // 长租客户
        input.setReferrerName("集成测试推荐人");
        input.setReferrerMobile("***********");
        input.setLongRentContractId("INTEGRATION_TEST_CONTRACT_" + System.currentTimeMillis());
        input.setOrgProperty("0"); // 外部
        input.setPayWay(1.0); // 预充值
        input.setLineLimitMonthly(10000.0);
        input.setInsideFlag(0); // 否
        input.setVehicleNo(""); // 不限制
        input.setMaxUnitPrice(new BigDecimal("100"));
        input.setOperatorId(999L);
        input.setOperatorName("集成测试操作员");
        
        return input;
    }
}
