package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dao.AgencyInfoMapper;
import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.rpc.dto.BaseResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LongRentMemberSyncServiceImpl 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class LongRentMemberSyncServiceImplTest {

    @Mock
    private AgencyInfoMapper agencyInfoMapper;

    @Mock
    private IVehicleRuleService vehicleRuleService;

    @Mock
    private IMmpDiscountRuleService mmpDiscountRuleService;

    @InjectMocks
    private LongRentMemberSyncServiceImpl longRentMemberSyncService;

    private LongRentMemberSyncInput validInput;
    private AgencyInfo existingAgency;

    @Before
    public void setUp() {
        // 准备有效的输入参数
        validInput = new LongRentMemberSyncInput();
        validInput.setAgencyName("测试企业");
        validInput.setContactName("张三");
        validInput.setContactMobile("13800138000");
        validInput.setLicenseNo("91110000000000000X");
        validInput.setTel("010-12345678");
        validInput.setEmail("<EMAIL>");
        validInput.setCooperateStatus(1);
        validInput.setMaxCarNumber(10);
        validInput.setDiscountRate(95.0);
        validInput.setBeneficiaryNumber(50);
        validInput.setExemptDepositAmount(new BigDecimal("1000"));
        validInput.setCreateWay(3); // 长租客户
        validInput.setOperatorId(1L);
        validInput.setOperatorName("测试操作员");

        // 准备现有企业信息
        existingAgency = new AgencyInfo();
        existingAgency.setAgencyId("TEST001");
        existingAgency.setAgencyName("测试企业");
        existingAgency.setCreateWay(3); // 长租客户创建
        existingAgency.setCooperateStatus(1); // 合作中
        existingAgency.setReferrerName("推荐人");
        existingAgency.setReferrerMobile("13900139000");
    }

    @Test
    public void testSyncLongRentMember_ScenarioA_Success() {
        // 场景A：企业会员不存在
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(Collections.emptyList());
        when(agencyInfoMapper.getMaxAgencyId()).thenReturn("TEST000");
        when(mmpDiscountRuleService.saveInit(anyString(), any(), any(), anyDouble(), anyInt())).thenReturn(1L);
        when(mmpDiscountRuleService.saveInitForPerson(anyString(), any(), any(), anyDouble())).thenReturn(2L);
        when(mmpDiscountRuleService.saveInitForPackage(anyString(), any(), any())).thenReturn(3L);
        when(agencyInfoMapper.save(any(AgencyInfo.class))).thenReturn(1);
        when(vehicleRuleService.createDefaultVehicleRule(anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "创建成功", "RULE001"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        assertEquals("企业会员创建成功", result.getMessage());
        verify(agencyInfoMapper).save(any(AgencyInfo.class));
        verify(vehicleRuleService).createDefaultVehicleRule(anyString(), anyLong(), anyString());
    }

    @Test
    public void testSyncLongRentMember_ScenarioB_LongRentCustomer() {
        // 场景B：企业会员存在，用车规则不存在，长租客户创建的企业
        List<AgencyInfo> agencies = new ArrayList<>();
        agencies.add(existingAgency);
        
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(agencies);
        when(vehicleRuleService.hasVehicleRule(anyString())).thenReturn(false);
        when(agencyInfoMapper.updateByPrimaryKey(any(AgencyInfo.class))).thenReturn(1);
        when(vehicleRuleService.createDefaultVehicleRule(anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "创建成功", "RULE001"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        assertEquals("企业会员已创建，信息已更新", result.getMessage());
        verify(agencyInfoMapper).updateByPrimaryKey(any(AgencyInfo.class));
        verify(vehicleRuleService).createDefaultVehicleRule(anyString(), anyLong(), anyString());
    }

    @Test
    public void testSyncLongRentMember_ScenarioB_GovernmentContract_Paused() {
        // 场景B：企业会员存在，用车规则不存在，政企框架合同创建且状态为暂停中
        existingAgency.setCreateWay(2); // 政企框架合同
        existingAgency.setCooperateStatus(2); // 暂停中
        
        List<AgencyInfo> agencies = new ArrayList<>();
        agencies.add(existingAgency);
        
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(agencies);
        when(vehicleRuleService.hasVehicleRule(anyString())).thenReturn(false);
        when(agencyInfoMapper.updateByPrimaryKey(any(AgencyInfo.class))).thenReturn(1);
        when(vehicleRuleService.createDefaultVehicleRule(anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "创建成功", "RULE001"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        assertEquals("企业会员已创建，信息已更新", result.getMessage());
        verify(agencyInfoMapper).updateByPrimaryKey(any(AgencyInfo.class));
        verify(vehicleRuleService).createDefaultVehicleRule(anyString(), anyLong(), anyString());
    }

    @Test
    public void testSyncLongRentMember_ScenarioB_GovernmentContract_Active() {
        // 场景B：企业会员存在，用车规则不存在，政企框架合同创建且状态为合作中
        existingAgency.setCreateWay(2); // 政企框架合同
        existingAgency.setCooperateStatus(1); // 合作中
        
        List<AgencyInfo> agencies = new ArrayList<>();
        agencies.add(existingAgency);
        
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(agencies);
        when(vehicleRuleService.hasVehicleRule(anyString())).thenReturn(false);
        when(vehicleRuleService.createDefaultVehicleRule(anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "创建成功", "RULE001"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        assertEquals("企业会员已创建，信息无法更新", result.getMessage());
        verify(agencyInfoMapper, never()).updateByPrimaryKey(any(AgencyInfo.class));
        verify(vehicleRuleService).createDefaultVehicleRule(anyString(), anyLong(), anyString());
    }

    @Test
    public void testSyncLongRentMember_ScenarioC_LongRentCustomer() {
        // 场景C：企业会员存在，用车规则存在，长租客户创建的企业
        List<AgencyInfo> agencies = new ArrayList<>();
        agencies.add(existingAgency);
        
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(agencies);
        when(vehicleRuleService.hasVehicleRule(anyString())).thenReturn(true);
        when(vehicleRuleService.getDefaultVehicleRuleId(anyString())).thenReturn("RULE001");
        when(agencyInfoMapper.updateByPrimaryKey(any(AgencyInfo.class))).thenReturn(1);
        when(vehicleRuleService.updateToDefaultVehicleRule(anyString(), anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "更新成功"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        assertEquals("企业会员已创建，信息已更新", result.getMessage());
        verify(agencyInfoMapper).updateByPrimaryKey(any(AgencyInfo.class));
        verify(vehicleRuleService).updateToDefaultVehicleRule(anyString(), anyString(), anyLong(), anyString());
    }

    @Test
    public void testSyncLongRentMember_InvalidInput_EmptyAgencyName() {
        // 测试无效输入：企业名称为空
        validInput.setAgencyName("");

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(-1, result.getCode());
        assertEquals("企业名称不能为空", result.getMessage());
    }

    @Test
    public void testSyncLongRentMember_InvalidInput_EmptyContactName() {
        // 测试无效输入：联系人姓名为空
        validInput.setContactName("");

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(-1, result.getCode());
        assertEquals("联系人姓名不能为空", result.getMessage());
    }

    @Test
    public void testSyncLongRentMember_InvalidInput_EmptyContactMobile() {
        // 测试无效输入：联系人手机号为空
        validInput.setContactMobile("");

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(-1, result.getCode());
        assertEquals("联系人手机号不能为空", result.getMessage());
    }

    @Test
    public void testSyncLongRentMember_InvalidInput_EmptyLicenseNo() {
        // 测试无效输入：企业营业执照号为空
        validInput.setLicenseNo("");

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(-1, result.getCode());
        assertEquals("企业营业执照号不能为空", result.getMessage());
    }

    @Test
    public void testSyncLongRentMember_LongContactName() {
        // 测试联系人姓名过长的情况
        validInput.setContactName("这是一个非常非常非常非常非常长的联系人姓名，超过了20个字符的限制");
        
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenReturn(Collections.emptyList());
        when(agencyInfoMapper.getMaxAgencyId()).thenReturn("TEST000");
        when(mmpDiscountRuleService.saveInit(anyString(), any(), any(), anyDouble(), anyInt())).thenReturn(1L);
        when(mmpDiscountRuleService.saveInitForPerson(anyString(), any(), any(), anyDouble())).thenReturn(2L);
        when(mmpDiscountRuleService.saveInitForPackage(anyString(), any(), any())).thenReturn(3L);
        when(agencyInfoMapper.save(any(AgencyInfo.class))).thenReturn(1);
        when(vehicleRuleService.createDefaultVehicleRule(anyString(), anyLong(), anyString()))
                .thenReturn(new BaseResponse(0, "创建成功", "RULE001"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(0, result.getCode());
        // 验证联系人姓名被截取为20个字符
        assertEquals(20, validInput.getContactName().length());
    }

    @Test
    public void testSyncLongRentMember_Exception() {
        // 测试异常情况
        when(agencyInfoMapper.getAgencyInfoByAgencyName(anyString())).thenThrow(new RuntimeException("数据库异常"));

        BaseResponse result = longRentMemberSyncService.syncLongRentMember(validInput);

        assertEquals(-1, result.getCode());
        assertTrue(result.getMessage().contains("长租会员同步失败"));
    }
}
