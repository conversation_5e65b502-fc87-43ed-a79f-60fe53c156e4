package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dto.MemberSyncInput;
import com.extracme.evcard.rpc.dto.BaseResponse;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员同步功能使用示例
 * 
 * 这个类展示了如何使用会员同步功能的各种场景
 */
public class MemberSyncUsageExample {

    private IMemberSyncService memberSyncService;

    /**
     * 示例1：创建新的企业会员（场景A）
     */
    public void example1_CreateNewAgency() {
        System.out.println("=== 示例1：创建新的企业会员 ===");
        
        MemberSyncInput input = new MemberSyncInput();
        input.setAgencyName("新创建企业示例");
        input.setContactName("张三");
        input.setContactMobile("13800138000");
        input.setLicenseNo("91110000000000001X");
        input.setTel("010-12345678");
        input.setEmail("<EMAIL>");
        input.setAddress("北京市朝阳区示例地址1号");
        input.setCooperateStatus(1); // 合作中
        input.setMaxCarNumber(10);
        input.setDiscountRate(95.0);
        input.setBeneficiaryNumber(50);
        input.setExemptDepositAmount(new BigDecimal("1000"));
        input.setCreateWay(3); // 长租客户
        input.setOperatorId(1L);
        input.setOperatorName("系统管理员");
        
        BaseResponse response = memberSyncService.syncMember(input);
        
        if (response.getCode() == 0) {
            System.out.println("✓ 创建成功：" + response.getMessage());
            System.out.println("  企业ID：" + response.getData());
        } else {
            System.out.println("✗ 创建失败：" + response.getMessage());
        }
    }

    /**
     * 示例2：更新长租客户创建的企业（场景B/C）
     */
    public void example2_UpdateLongRentAgency() {
        System.out.println("\n=== 示例2：更新长租客户创建的企业 ===");
        
        MemberSyncInput input = new MemberSyncInput();
        input.setAgencyName("已存在的长租企业");
        input.setContactName("李四");
        input.setContactMobile("13900139000");
        input.setLicenseNo("91110000000000002X");
        input.setTel("010-87654321");
        input.setEmail("<EMAIL>");
        input.setAddress("北京市海淀区示例地址2号");
        input.setRemark("更新后的备注信息");
        input.setCooperateStatus(1); // 合作中
        input.setMaxCarNumber(20);
        input.setDiscountRate(90.0);
        input.setBeneficiaryNumber(100);
        input.setExemptDepositAmount(new BigDecimal("2000"));
        input.setCreateWay(3); // 长租客户
        input.setOperatorId(2L);
        input.setOperatorName("业务经理");
        
        BaseResponse response = memberSyncService.syncMember(input);
        
        if (response.getCode() == 0) {
            System.out.println("✓ 更新成功：" + response.getMessage());
        } else {
            System.out.println("✗ 更新失败：" + response.getMessage());
        }
    }

    /**
     * 示例3：尝试更新政企框架合同创建的合作中企业（应该不允许更新）
     */
    public void example3_UpdateGovernmentContractActiveAgency() {
        System.out.println("\n=== 示例3：尝试更新政企框架合同创建的合作中企业 ===");
        
        MemberSyncInput input = new MemberSyncInput();
        input.setAgencyName("政企框架合同企业");
        input.setContactName("王五");
        input.setContactMobile("13700137000");
        input.setLicenseNo("91110000000000003X");
        input.setTel("010-11111111");
        input.setEmail("<EMAIL>");
        input.setAddress("北京市西城区示例地址3号");
        input.setCooperateStatus(1); // 合作中
        input.setMaxCarNumber(15);
        input.setDiscountRate(85.0);
        input.setBeneficiaryNumber(75);
        input.setExemptDepositAmount(new BigDecimal("1500"));
        input.setCreateWay(2); // 政企框架合同
        input.setOperatorId(3L);
        input.setOperatorName("客户经理");
        
        BaseResponse response = memberSyncService.syncMember(input);
        
        if (response.getCode() == 0) {
            System.out.println("✓ 处理成功：" + response.getMessage());
            if (response.getMessage().contains("无法更新")) {
                System.out.println("  符合预期：政企框架合同创建的合作中企业不允许更新信息");
            }
        } else {
            System.out.println("✗ 处理失败：" + response.getMessage());
        }
    }

    /**
     * 示例4：更新政企框架合同创建的暂停中企业（应该允许更新）
     */
    public void example4_UpdateGovernmentContractPausedAgency() {
        System.out.println("\n=== 示例4：更新政企框架合同创建的暂停中企业 ===");
        
        MemberSyncInput input = new MemberSyncInput();
        input.setAgencyName("政企框架合同暂停企业");
        input.setContactName("赵六");
        input.setContactMobile("13600136000");
        input.setLicenseNo("91110000000000004X");
        input.setTel("010-22222222");
        input.setEmail("<EMAIL>");
        input.setAddress("北京市东城区示例地址4号");
        input.setCooperateStatus(2); // 暂停中
        input.setMaxCarNumber(25);
        input.setDiscountRate(80.0);
        input.setBeneficiaryNumber(120);
        input.setExemptDepositAmount(new BigDecimal("2500"));
        input.setCreateWay(2); // 政企框架合同
        input.setOperatorId(4L);
        input.setOperatorName("运营专员");
        
        BaseResponse response = memberSyncService.syncMember(input);
        
        if (response.getCode() == 0) {
            System.out.println("✓ 处理成功：" + response.getMessage());
            if (response.getMessage().contains("已更新")) {
                System.out.println("  符合预期：政企框架合同创建的暂停中企业允许更新信息");
            }
        } else {
            System.out.println("✗ 处理失败：" + response.getMessage());
        }
    }

    /**
     * 示例5：处理无效输入
     */
    public void example5_HandleInvalidInput() {
        System.out.println("\n=== 示例5：处理无效输入 ===");
        
        // 测试空企业名称
        MemberSyncInput input1 = new MemberSyncInput();
        input1.setAgencyName(""); // 空企业名称
        input1.setContactName("测试联系人");
        input1.setContactMobile("13500135000");
        input1.setLicenseNo("91110000000000005X");
        
        BaseResponse response1 = memberSyncService.syncMember(input1);
        System.out.println("空企业名称测试：" + response1.getMessage());
        
        // 测试空联系人手机号
        MemberSyncInput input2 = new MemberSyncInput();
        input2.setAgencyName("测试企业");
        input2.setContactName("测试联系人");
        input2.setContactMobile(""); // 空手机号
        input2.setLicenseNo("91110000000000006X");
        
        BaseResponse response2 = memberSyncService.syncMember(input2);
        System.out.println("空手机号测试：" + response2.getMessage());
        
        // 测试过长的联系人姓名
        MemberSyncInput input3 = new MemberSyncInput();
        input3.setAgencyName("测试企业长姓名");
        input3.setContactName("这是一个非常非常非常非常非常长的联系人姓名，超过了20个字符的限制");
        input3.setContactMobile("13400134000");
        input3.setLicenseNo("91110000000000007X");
        input3.setOperatorId(5L);
        input3.setOperatorName("测试员");
        
        BaseResponse response3 = memberSyncService.syncMember(input3);
        System.out.println("长姓名测试：" + response3.getMessage());
        System.out.println("  截取后的姓名：" + input3.getContactName() + "（长度：" + input3.getContactName().length() + "）");
    }

    /**
     * 示例6：完整的企业信息同步
     */
    public void example6_CompleteAgencySync() {
        System.out.println("\n=== 示例6：完整的企业信息同步 ===");
        
        MemberSyncInput input = new MemberSyncInput();
        
        // 基本信息
        input.setAgencyName("完整信息企业示例");
        input.setContactName("完整信息联系人");
        input.setContactMobile("13300133000");
        input.setLicenseNo("91110000000000008X");
        input.setTel("010-33333333");
        input.setEmail("<EMAIL>");
        input.setFax("010-44444444");
        input.setAddress("北京市丰台区完整地址8号");
        input.setRemark("这是一个包含完整信息的企业同步示例");
        
        // 合作信息
        input.setCooperateStartTime(new Date());
        input.setCooperateEndTime(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 一年后
        input.setCooperateStatus(1); // 合作中
        
        // 业务信息
        input.setMaxCarNumber(30);
        input.setDiscountRate(88.0);
        input.setBeneficiaryNumber(150);
        input.setExemptDepositAmount(new BigDecimal("3000"));
        input.setDepositGuarantor(1); // 企业支付
        input.setOrderPayer(1); // 企业支付
        input.setDefaultingParty(1); // 企业承担
        input.setBusinessSource(4); // 长租客户
        input.setCreateWay(3); // 长租客户
        
        // 推荐人信息
        input.setReferrerName("推荐人姓名");
        input.setReferrerMobile("***********");
        
        // 其他信息
        input.setLongRentContractId("CONTRACT_COMPLETE_" + System.currentTimeMillis());
        input.setOrgProperty("0"); // 外部
        input.setPayWay(1.0); // 预充值
        input.setLineLimitMonthly(50000.0);
        input.setInsideFlag(0); // 否
        input.setVehicleNo("京A,京B"); // 限制车牌
        input.setMaxUnitPrice(new BigDecimal("200"));
        
        // 操作人信息
        input.setOperatorId(6L);
        input.setOperatorName("完整信息操作员");
        
        BaseResponse response = memberSyncService.syncMember(input);
        
        if (response.getCode() == 0) {
            System.out.println("✓ 完整同步成功：" + response.getMessage());
            if (response.getData() != null) {
                System.out.println("  企业ID：" + response.getData());
            }
        } else {
            System.out.println("✗ 完整同步失败：" + response.getMessage());
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        System.out.println("会员同步功能使用示例");
        System.out.println("========================");
        
        example1_CreateNewAgency();
        example2_UpdateLongRentAgency();
        example3_UpdateGovernmentContractActiveAgency();
        example4_UpdateGovernmentContractPausedAgency();
        example5_HandleInvalidInput();
        example6_CompleteAgencySync();
        
        System.out.println("\n所有示例执行完成！");
    }

    // Setter for dependency injection
    public void setMemberSyncService(IMemberSyncService memberSyncService) {
        this.memberSyncService = memberSyncService;
    }
}
