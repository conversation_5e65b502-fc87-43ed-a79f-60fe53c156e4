package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.BeanCopyUtils;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dao.AgencyInfoMapper;
import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.mmp.util.AgencyIdUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 长租会员同步服务实现类
 */
@Slf4j
@Service
public class LongRentMemberSyncServiceImpl extends LongRentMemberSyncProcessor implements ILongRentMemberSyncService {

    @Resource
    private AgencyInfoMapper agencyInfoMapper;

    @Resource
    private IVehicleRuleService vehicleRuleService;

    @Resource
    private IMmpDiscountRuleService mmpDiscountRuleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse syncLongRentMember(LongRentMemberSyncInput input) {
        log.info("开始执行长租会员同步逻辑，输入参数：{}", JSON.toJSONString(input));
        
        // 1. 参数校验
        BaseResponse validationResult = validateInput(input);
        if (validationResult.getCode() != 0) {
            return validationResult;
        }
        
        try {
            // 2. 根据企业名称查询现有企业信息
            List<AgencyInfo> existingAgencies = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());
            
            if (CollectionUtils.isEmpty(existingAgencies)) {
                // 场景A：企业会员不存在
                return handleScenarioA(input);
            } else {
                AgencyInfo agencyInfo = existingAgencies.get(0);
                
                // 检查是否存在用车规则
                boolean hasVehicleRule = vehicleRuleService.hasVehicleRule(agencyInfo.getAgencyId());
                
                if (!hasVehicleRule) {
                    // 场景B：企业会员存在，但用车规则不存在
                    return handleScenarioB(agencyInfo, input);
                } else {
                    // 场景C：企业会员存在，且用车规则也存在
                    return handleScenarioC(agencyInfo, input);
                }
            }
            
        } catch (Exception e) {
            log.error("长租会员同步逻辑执行失败，企业名称：{}", input.getAgencyName(), e);
            return new BaseResponse(-1, "长租会员同步失败：" + e.getMessage());
        }
    }

    /**
     * 参数校验
     */
    private BaseResponse validateInput(LongRentMemberSyncInput input) {
        if (input == null) {
            return new BaseResponse(-1, "输入参数不能为空");
        }

        if (StringUtils.isEmpty(input.getAgencyName())) {
            return new BaseResponse(-1, "企业名称不能为空");
        }

        if (StringUtils.isEmpty(input.getContactName())) {
            return new BaseResponse(-1, "联系人姓名不能为空");
        }

        if (StringUtils.isEmpty(input.getContactMobile())) {
            return new BaseResponse(-1, "联系人手机号不能为空");
        }

        // 企业营业执照号非必填，不校验

        // 姓名长度过长时截取
        if (input.getContactName().length() > 20) {
            input.setContactName(input.getContactName().substring(0, 20));
        }

        // 设置默认值
        if (input.getOperatorId() == null) {
            input.setOperatorId(-1L);
        }
        if (StringUtils.isEmpty(input.getOperatorName())) {
            input.setOperatorName("长租会员同步自动操作");
        }

        return new BaseResponse(0, "参数校验通过");
    }

    @Override
    protected String createNewAgency(LongRentMemberSyncInput input) {
        log.info("开始创建新的企业会员，企业名称：{}", input.getAgencyName());
        
        try {
            // 1. 生成新的企业ID
            String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
            String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2);
            String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + 
                    AgencyIdUtils.getNextNodeId(last2Digit);
            
            // 2. 构建企业信息
            AgencyInfo agencyInfo = buildAgencyInfo(input, newAgencyId, true);
            
            // 3. 初始化折扣规则（按照字段映射表的固定值）
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(input.getOperatorId());
            operatorDTO.setOperatorName(input.getOperatorName());

            // 个人通用折扣和旺季折扣：95
            Double discountRate = 95.0;
            // 受益人数：50000
            Integer beneficiaryNumber = 50000;

            Long discountId = mmpDiscountRuleService.saveInit(newAgencyId, null, operatorDTO, 100.0, 0);
            Long discountPersonId = mmpDiscountRuleService.saveInitForPerson(newAgencyId, null, operatorDTO, discountRate);
            mmpDiscountRuleService.saveInitForPackage(newAgencyId, null, operatorDTO);
            
            agencyInfo.setDiscountId(discountId);
            agencyInfo.setDiscountPersonalId(discountPersonId);
            
            // 4. 保存企业信息
            int result = agencyInfoMapper.save(agencyInfo);
            if (result > 0) {
                log.info("创建企业会员成功，企业ID：{}", newAgencyId);
                return newAgencyId;
            } else {
                log.error("保存企业会员失败，企业名称：{}", input.getAgencyName());
                return null;
            }
            
        } catch (Exception e) {
            log.error("创建企业会员异常，企业名称：{}", input.getAgencyName(), e);
            return null;
        }
    }

    @Override
    protected void updateAgencyInfo(AgencyInfo agencyInfo, LongRentMemberSyncInput input, boolean keepReferrer) {
        log.info("开始更新企业信息，企业ID：{}，保持推荐人信息：{}", agencyInfo.getAgencyId(), keepReferrer);
        
        try {
            // 保存原有的推荐人信息
            String originalReferrerName = agencyInfo.getReferrerName();
            String originalReferrerMobile = agencyInfo.getReferrerMobile();
            
            // 更新企业信息
            buildAgencyInfoFromInput(agencyInfo, input);
            
            // 如果需要保持推荐人信息不变，则恢复原有值
            if (keepReferrer) {
                agencyInfo.setReferrerName(originalReferrerName);
                agencyInfo.setReferrerMobile(originalReferrerMobile);
            }
            
            // 设置更新信息
            agencyInfo.setUpdateOperId(input.getOperatorId());
            agencyInfo.setUpdatedUser(input.getOperatorName());
            agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            
            // 保存更新
            int result = agencyInfoMapper.updateByPrimaryKey(agencyInfo);
            if (result > 0) {
                log.info("更新企业信息成功，企业ID：{}", agencyInfo.getAgencyId());
            } else {
                log.error("更新企业信息失败，企业ID：{}", agencyInfo.getAgencyId());
            }
            
        } catch (Exception e) {
            log.error("更新企业信息异常，企业ID：{}", agencyInfo.getAgencyId(), e);
            throw new RuntimeException("更新企业信息失败", e);
        }
    }

    @Override
    protected BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName) {
        return vehicleRuleService.createDefaultVehicleRule(agencyId, operatorId, operatorName);
    }

    @Override
    protected BaseResponse updateToDefaultVehicleRule(String agencyId, String ruleId, Long operatorId, String operatorName) {
        return vehicleRuleService.updateToDefaultVehicleRule(agencyId, ruleId, operatorId, operatorName);
    }

    @Override
    protected String getDefaultVehicleRuleId(String agencyId) {
        return vehicleRuleService.getDefaultVehicleRuleId(agencyId);
    }

    /**
     * 构建企业信息对象
     */
    private AgencyInfo buildAgencyInfo(LongRentMemberSyncInput input, String agencyId, boolean isNew) {
        AgencyInfo agencyInfo = new AgencyInfo();

        if (isNew) {
            // 新建企业的基本设置
            agencyInfo.setAgencyId(agencyId);
            agencyInfo.setDiscountRule(1);
            agencyInfo.setAppKey("");
            agencyInfo.setOrgName("");
            agencyInfo.setStatus(2); // 0-暂停中，1-合作中，2-未开始
            agencyInfo.setCooperateStatus(0); // 0-未开始；1-合作中；2-已暂停
            agencyInfo.setMaxUnitPrice(new BigDecimal(1));
            agencyInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfo.setCreatedUser(input.getOperatorName());
            agencyInfo.setCreateOperId(input.getOperatorId());

            // 设置固定值（根据字段映射表）
            agencyInfo.setOrgProperty("0"); // 企业性质：0（外部）
            agencyInfo.setPayWay(1.0); // 结算方式：1.0（预付费）
            agencyInfo.setOrderPayer(2); // 订单支付方：2（个人支付）
            agencyInfo.setDepositGuarantor(2); // 押金担保方：2（个人支付）
            agencyInfo.setDefaultingParty(2); // 违约承担方：2（个人承担）
            agencyInfo.setBusinessSource(1); // 业务来源：1（政企客户）
            agencyInfo.setCreateWay(3); // 创建方式：3（长租客户）
            agencyInfo.setVehicleNo(""); // 车牌限制：""（不限制）
            agencyInfo.setExemptDepositAmount(new BigDecimal("0")); // 企业免押金额：0

            // 设置失效时间：当前日期+3年
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.YEAR, 3);
            agencyInfo.setCooperateStartTime(now);
            agencyInfo.setCooperateEndTime(calendar.getTime());
        }

        // 从输入参数构建企业信息
        buildAgencyInfoFromInput(agencyInfo, input);

        return agencyInfo;
    }

    /**
     * 从输入参数构建企业信息
     */
    private void buildAgencyInfoFromInput(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        // 基本信息
        agencyInfo.setAgencyName(input.getAgencyName());
        agencyInfo.setContact(input.getContactName());
        agencyInfo.setMobilePhone(input.getContactMobile());

        // 企业营业执照号（非必填）
        if (!StringUtils.isEmpty(input.getLicenseNo())) {
            agencyInfo.setLicenseNo(input.getLicenseNo());
        }

        // 联系人邮箱
        if (!StringUtils.isEmpty(input.getContactEmail())) {
            agencyInfo.setMail(input.getContactEmail());
        }

        // 备注：客户ID
        if (!StringUtils.isEmpty(input.getCustomerId())) {
            agencyInfo.setRemark(input.getCustomerId());
        }

        // 推荐人信息（客户所属销售）
        if (!StringUtils.isEmpty(input.getReferrerName())) {
            agencyInfo.setReferrerName(input.getReferrerName());
        } else {
            agencyInfo.setReferrerName("");
        }
        if (!StringUtils.isEmpty(input.getReferrerMobile())) {
            agencyInfo.setReferrerMobile(input.getReferrerMobile());
        } else {
            agencyInfo.setReferrerMobile("");
        }
    }
}
