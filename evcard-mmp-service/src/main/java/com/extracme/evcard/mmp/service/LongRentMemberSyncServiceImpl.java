package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.BeanCopyUtils;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dao.AgencyInfoMapper;
import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.mmp.util.AgencyIdUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 长租会员同步服务实现类
 */
@Slf4j
@Service
public class LongRentMemberSyncServiceImpl extends LongRentMemberSyncProcessor implements ILongRentMemberSyncService {

    @Resource
    private AgencyInfoMapper agencyInfoMapper;

    @Resource
    private IVehicleRuleService vehicleRuleService;

    @Resource
    private IMmpDiscountRuleService mmpDiscountRuleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse syncLongRentMember(LongRentMemberSyncInput input) {
        log.info("开始执行长租会员同步逻辑，输入参数：{}", JSON.toJSONString(input));
        
        // 1. 参数校验
        BaseResponse validationResult = validateInput(input);
        if (validationResult.getCode() != 0) {
            return validationResult;
        }
        
        try {
            // 2. 根据企业名称查询现有企业信息
            List<AgencyInfo> existingAgencies = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());
            
            if (CollectionUtils.isEmpty(existingAgencies)) {
                // 场景A：企业会员不存在
                return handleScenarioA(input);
            } else {
                AgencyInfo agencyInfo = existingAgencies.get(0);
                
                // 检查是否存在用车规则
                boolean hasVehicleRule = vehicleRuleService.hasVehicleRule(agencyInfo.getAgencyId());
                
                if (!hasVehicleRule) {
                    // 场景B：企业会员存在，但用车规则不存在
                    return handleScenarioB(agencyInfo, input);
                } else {
                    // 场景C：企业会员存在，且用车规则也存在
                    return handleScenarioC(agencyInfo, input);
                }
            }
            
        } catch (Exception e) {
            log.error("长租会员同步逻辑执行失败，企业名称：{}", input.getAgencyName(), e);
            return new BaseResponse(-1, "长租会员同步失败：" + e.getMessage());
        }
    }

    /**
     * 参数校验
     */
    private BaseResponse validateInput(LongRentMemberSyncInput input) {
        if (input == null) {
            return new BaseResponse(-1, "输入参数不能为空");
        }
        
        if (StringUtils.isEmpty(input.getAgencyName())) {
            return new BaseResponse(-1, "企业名称不能为空");
        }
        
        if (StringUtils.isEmpty(input.getContactName())) {
            return new BaseResponse(-1, "联系人姓名不能为空");
        }
        
        if (StringUtils.isEmpty(input.getContactMobile())) {
            return new BaseResponse(-1, "联系人手机号不能为空");
        }
        
        if (StringUtils.isEmpty(input.getLicenseNo())) {
            return new BaseResponse(-1, "企业营业执照号不能为空");
        }
        
        // 姓名长度过长时截取
        if (input.getContactName().length() > 20) {
            input.setContactName(input.getContactName().substring(0, 20));
        }
        
        // 设置默认值
        if (input.getOperatorId() == null) {
            input.setOperatorId(-1L);
        }
        if (StringUtils.isEmpty(input.getOperatorName())) {
            input.setOperatorName("长租会员同步自动操作");
        }
        
        return new BaseResponse(0, "参数校验通过");
    }

    @Override
    protected String createNewAgency(LongRentMemberSyncInput input) {
        log.info("开始创建新的企业会员，企业名称：{}", input.getAgencyName());
        
        try {
            // 1. 生成新的企业ID
            String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
            String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2);
            String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + 
                    AgencyIdUtils.getNextNodeId(last2Digit);
            
            // 2. 构建企业信息
            AgencyInfo agencyInfo = buildAgencyInfo(input, newAgencyId, true);
            
            // 3. 初始化折扣规则
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(input.getOperatorId());
            operatorDTO.setOperatorName(input.getOperatorName());
            
            Double discountRate = input.getDiscountRate() != null ? input.getDiscountRate() : 100.0;
            Integer beneficiaryNumber = input.getBeneficiaryNumber() != null ? input.getBeneficiaryNumber() : 0;
            
            Long discountId = mmpDiscountRuleService.saveInit(newAgencyId, null, operatorDTO, 100.0, 0);
            Long discountPersonId = mmpDiscountRuleService.saveInitForPerson(newAgencyId, null, operatorDTO, discountRate);
            mmpDiscountRuleService.saveInitForPackage(newAgencyId, null, operatorDTO);
            
            agencyInfo.setDiscountId(discountId);
            agencyInfo.setDiscountPersonalId(discountPersonId);
            
            // 4. 保存企业信息
            int result = agencyInfoMapper.save(agencyInfo);
            if (result > 0) {
                log.info("创建企业会员成功，企业ID：{}", newAgencyId);
                return newAgencyId;
            } else {
                log.error("保存企业会员失败，企业名称：{}", input.getAgencyName());
                return null;
            }
            
        } catch (Exception e) {
            log.error("创建企业会员异常，企业名称：{}", input.getAgencyName(), e);
            return null;
        }
    }

    @Override
    protected void updateAgencyInfo(AgencyInfo agencyInfo, LongRentMemberSyncInput input, boolean keepReferrer) {
        log.info("开始更新企业信息，企业ID：{}，保持推荐人信息：{}", agencyInfo.getAgencyId(), keepReferrer);
        
        try {
            // 保存原有的推荐人信息
            String originalReferrerName = agencyInfo.getReferrerName();
            String originalReferrerMobile = agencyInfo.getReferrerMobile();
            
            // 更新企业信息
            buildAgencyInfoFromInput(agencyInfo, input);
            
            // 如果需要保持推荐人信息不变，则恢复原有值
            if (keepReferrer) {
                agencyInfo.setReferrerName(originalReferrerName);
                agencyInfo.setReferrerMobile(originalReferrerMobile);
            }
            
            // 设置更新信息
            agencyInfo.setUpdateOperId(input.getOperatorId());
            agencyInfo.setUpdatedUser(input.getOperatorName());
            agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            
            // 保存更新
            int result = agencyInfoMapper.updateByPrimaryKey(agencyInfo);
            if (result > 0) {
                log.info("更新企业信息成功，企业ID：{}", agencyInfo.getAgencyId());
            } else {
                log.error("更新企业信息失败，企业ID：{}", agencyInfo.getAgencyId());
            }
            
        } catch (Exception e) {
            log.error("更新企业信息异常，企业ID：{}", agencyInfo.getAgencyId(), e);
            throw new RuntimeException("更新企业信息失败", e);
        }
    }

    @Override
    protected BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName) {
        return vehicleRuleService.createDefaultVehicleRule(agencyId, operatorId, operatorName);
    }

    @Override
    protected BaseResponse updateToDefaultVehicleRule(String agencyId, String ruleId, Long operatorId, String operatorName) {
        return vehicleRuleService.updateToDefaultVehicleRule(agencyId, ruleId, operatorId, operatorName);
    }

    @Override
    protected String getDefaultVehicleRuleId(String agencyId) {
        return vehicleRuleService.getDefaultVehicleRuleId(agencyId);
    }

    /**
     * 构建企业信息对象
     */
    private AgencyInfo buildAgencyInfo(LongRentMemberSyncInput input, String agencyId, boolean isNew) {
        AgencyInfo agencyInfo = new AgencyInfo();
        
        if (isNew) {
            // 新建企业的基本设置
            agencyInfo.setAgencyId(agencyId);
            agencyInfo.setDiscountRule(1);
            agencyInfo.setAppKey("");
            agencyInfo.setOrgName("");
            agencyInfo.setStatus(2); // 0-暂停中，1-合作中，2-未开始
            agencyInfo.setCooperateStatus(0); // 0-未开始；1-合作中；2-已暂停
            agencyInfo.setMaxUnitPrice(new BigDecimal(1));
            agencyInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfo.setCreatedUser(input.getOperatorName());
            agencyInfo.setCreateOperId(input.getOperatorId());
        }
        
        // 从输入参数构建企业信息
        buildAgencyInfoFromInput(agencyInfo, input);
        
        return agencyInfo;
    }

    /**
     * 从输入参数构建企业信息
     */
    private void buildAgencyInfoFromInput(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        agencyInfo.setAgencyName(input.getAgencyName());
        agencyInfo.setContact(input.getContactName());
        agencyInfo.setMobilePhone(input.getContactMobile());
        agencyInfo.setLicenseNo(input.getLicenseNo());
        agencyInfo.setTel(input.getTel());
        agencyInfo.setMail(input.getEmail());
        agencyInfo.setFax(input.getFax());
        agencyInfo.setAddress(input.getAddress());
        agencyInfo.setRemark(input.getRemark());
        agencyInfo.setCooperateStartTime(input.getCooperateStartTime());
        agencyInfo.setCooperateEndTime(input.getCooperateEndTime());
        
        // 设置默认值或从输入参数获取
        if (input.getCooperateStatus() != null) {
            agencyInfo.setCooperateStatus(input.getCooperateStatus());
        }
        if (input.getMaxCarNumber() != null) {
            agencyInfo.setVehicleThreshold(input.getMaxCarNumber().doubleValue());
        }
        if (input.getExemptDepositAmount() != null) {
            agencyInfo.setExemptDepositAmount(input.getExemptDepositAmount());
        }
        if (input.getDepositGuarantor() != null) {
            agencyInfo.setDepositGuarantor(input.getDepositGuarantor());
        }
        if (input.getOrderPayer() != null) {
            agencyInfo.setOrderPayer(input.getOrderPayer());
        }
        if (input.getDefaultingParty() != null) {
            agencyInfo.setDefaultingParty(input.getDefaultingParty());
        }
        if (input.getBusinessSource() != null) {
            agencyInfo.setBusinessSource(input.getBusinessSource());
        }
        if (input.getCreateWay() != null) {
            agencyInfo.setCreateWay(input.getCreateWay());
        }
        if (input.getLongRentContractId() != null) {
            agencyInfo.setlongRentContractId(input.getLongRentContractId());
        }
        if (input.getOrgProperty() != null) {
            agencyInfo.setOrgProperty(input.getOrgProperty());
        } else {
            agencyInfo.setOrgProperty("0"); // 默认外部
        }
        if (input.getPayWay() != null) {
            agencyInfo.setPayWay(input.getPayWay());
        }
        if (input.getLineLimitMonthly() != null) {
            agencyInfo.setLineLimitMonthly(input.getLineLimitMonthly());
        }
        if (input.getInsideFlag() != null) {
            agencyInfo.setInsideFlag(input.getInsideFlag());
        } else {
            agencyInfo.setInsideFlag(0); // 默认否
        }
        if (input.getVehicleNo() != null) {
            agencyInfo.setVehicleNo(input.getVehicleNo());
        } else {
            agencyInfo.setVehicleNo(""); // 默认不限制
        }
        if (input.getMaxUnitPrice() != null) {
            agencyInfo.setMaxUnitPrice(input.getMaxUnitPrice());
        }
        
        // 推荐人信息
        if (!StringUtils.isEmpty(input.getReferrerName())) {
            agencyInfo.setReferrerName(input.getReferrerName());
        } else {
            agencyInfo.setReferrerName("");
        }
        if (!StringUtils.isEmpty(input.getReferrerMobile())) {
            agencyInfo.setReferrerMobile(input.getReferrerMobile());
        } else {
            agencyInfo.setReferrerMobile("");
        }
    }
}
