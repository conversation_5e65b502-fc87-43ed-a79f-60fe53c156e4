package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dto.LongRentMemberSyncInput;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 长租会员同步业务逻辑处理器
 * 负责根据不同场景执行相应的业务逻辑
 */
@Slf4j
@Component
public class LongRentMemberSyncProcessor {

    /**
     * 处理场景A：企业会员不存在
     * 基于长租客户信息创建新的企业会员，同时创建默认用车规则
     * 
     * @param input 长租会员同步输入参数
     * @return 处理结果
     */
    public BaseResponse handleScenarioA(LongRentMemberSyncInput input) {
        log.info("处理场景A：企业会员不存在，创建新企业会员。企业名称：{}", input.getAgencyName());
        
        try {
            // 1. 创建新的企业会员
            String agencyId = createNewAgency(input);
            if (agencyId == null) {
                return new BaseResponse(-1, "创建企业会员失败");
            }
            
            // 2. 创建默认用车规则
            BaseResponse ruleResult = createDefaultVehicleRule(agencyId, input.getOperatorId(), input.getOperatorName());
            if (ruleResult.getCode() != 0) {
                log.warn("创建默认用车规则失败，但企业会员已创建成功。企业ID：{}，错误信息：{}", agencyId, ruleResult.getMessage());
            }
            
            return new BaseResponse(0, "企业会员创建成功");
            
        } catch (Exception e) {
            log.error("处理场景A失败，企业名称：{}", input.getAgencyName(), e);
            return new BaseResponse(-1, "创建企业会员失败：" + e.getMessage());
        }
    }

    /**
     * 处理场景B：企业会员存在，但用车规则不存在
     * 根据企业创建方式执行不同逻辑
     * 
     * @param agencyInfo 现有企业信息
     * @param input 长租会员同步输入参数
     * @return 处理结果
     */
    public BaseResponse handleScenarioB(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        log.info("处理场景B：企业会员存在但用车规则不存在。企业ID：{}，创建方式：{}", 
                agencyInfo.getAgencyId(), agencyInfo.getCreateWay());
        
        try {
            // 1. 根据创建方式和合作状态判断是否需要更新企业信息
            boolean shouldUpdate = shouldUpdateAgencyInfo(agencyInfo, input);
            String message;
            
            if (shouldUpdate) {
                // 更新企业信息（保持推荐人信息不变）
                updateAgencyInfo(agencyInfo, input, true);
                message = "企业会员已创建，信息已更新";
            } else {
                message = "企业会员已创建，信息无法更新";
            }
            
            // 2. 创建默认用车规则
            BaseResponse ruleResult = createDefaultVehicleRule(agencyInfo.getAgencyId(), 
                    input.getOperatorId(), input.getOperatorName());
            if (ruleResult.getCode() != 0) {
                log.warn("创建默认用车规则失败。企业ID：{}，错误信息：{}", 
                        agencyInfo.getAgencyId(), ruleResult.getMessage());
            }
            
            return new BaseResponse(0, message);
            
        } catch (Exception e) {
            log.error("处理场景B失败，企业ID：{}", agencyInfo.getAgencyId(), e);
            return new BaseResponse(-1, "处理企业会员失败：" + e.getMessage());
        }
    }

    /**
     * 处理场景C：企业会员存在，且用车规则也存在
     * 根据企业创建方式执行不同逻辑
     * 
     * @param agencyInfo 现有企业信息
     * @param input 长租会员同步输入参数
     * @return 处理结果
     */
    public BaseResponse handleScenarioC(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        log.info("处理场景C：企业会员和用车规则都存在。企业ID：{}，创建方式：{}", 
                agencyInfo.getAgencyId(), agencyInfo.getCreateWay());
        
        try {
            // 1. 根据创建方式和合作状态判断是否需要更新企业信息
            boolean shouldUpdate = shouldUpdateAgencyInfo(agencyInfo, input);
            String message;
            
            if (shouldUpdate) {
                // 更新企业信息（保持推荐人信息不变）
                updateAgencyInfo(agencyInfo, input, true);
                
                // 更新用车规则为默认规则
                String ruleId = getDefaultVehicleRuleId(agencyInfo.getAgencyId());
                if (ruleId != null) {
                    BaseResponse ruleResult = updateToDefaultVehicleRule(agencyInfo.getAgencyId(), ruleId, 
                            input.getOperatorId(), input.getOperatorName());
                    if (ruleResult.getCode() != 0) {
                        log.warn("更新用车规则失败。企业ID：{}，规则ID：{}，错误信息：{}", 
                                agencyInfo.getAgencyId(), ruleId, ruleResult.getMessage());
                    }
                }
                
                message = "企业会员已创建，信息已更新";
            } else {
                message = "企业会员已创建，信息无法更新";
            }
            
            return new BaseResponse(0, message);
            
        } catch (Exception e) {
            log.error("处理场景C失败，企业ID：{}", agencyInfo.getAgencyId(), e);
            return new BaseResponse(-1, "处理企业会员失败：" + e.getMessage());
        }
    }

    /**
     * 判断是否应该更新企业信息
     * 
     * @param agencyInfo 现有企业信息
     * @param input 长租会员同步输入参数
     * @return 是否应该更新
     */
    private boolean shouldUpdateAgencyInfo(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        Integer createWay = agencyInfo.getCreateWay();
        Integer cooperateStatus = agencyInfo.getCooperateStatus();
        
        // 长租客户创建的企业：直接更新
        if (createWay != null && createWay == 3) {
            return true;
        }
        
        // 政企框架合同创建的企业或会员系统创建的企业：只有在暂停中状态才更新
        if ((createWay != null && (createWay == 1 || createWay == 2)) && 
            (cooperateStatus != null && cooperateStatus == 2)) {
            return true;
        }
        
        return false;
    }

    // 以下方法需要在实现类中具体实现
    protected String createNewAgency(LongRentMemberSyncInput input) {
        // 具体实现在 LongRentMemberSyncServiceImpl 中
        throw new UnsupportedOperationException("需要在实现类中重写此方法");
    }

    protected void updateAgencyInfo(AgencyInfo agencyInfo, LongRentMemberSyncInput input, boolean keepReferrer) {
        // 具体实现在 LongRentMemberSyncServiceImpl 中
        throw new UnsupportedOperationException("需要在实现类中重写此方法");
    }

    protected BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName) {
        // 具体实现在 LongRentMemberSyncServiceImpl 中
        throw new UnsupportedOperationException("需要在实现类中重写此方法");
    }

    protected BaseResponse updateToDefaultVehicleRule(String agencyId, String ruleId, Long operatorId, String operatorName) {
        // 具体实现在 LongRentMemberSyncServiceImpl 中
        throw new UnsupportedOperationException("需要在实现类中重写此方法");
    }

    protected String getDefaultVehicleRuleId(String agencyId) {
        // 具体实现在 LongRentMemberSyncServiceImpl 中
        throw new UnsupportedOperationException("需要在实现类中重写此方法");
    }
}
