package com.extracme.evcard.mmp.service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.core.dto.CommBaseResponse;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.agency.AddAgencySecondAppKeyRelationDTO;
import com.extracme.evcard.membership.core.dto.agency.AgencySecondAppKeyRelationDTO;
import com.extracme.evcard.membership.core.service.agency.IMembershipAgencyService;
import com.extracme.evcard.mmp.common.*;
import com.extracme.evcard.mmp.dao.*;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.mmp.model.MmpAgencyDiscountLog;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.pay.service.IMemAccountService;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

import java.util.*;

@Service
public class AgencyServiceImpl implements IAgencyService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    AgencyInfoMapper agencyInfoMapper;

    @Resource
    AppKeyManagerMapper appKeyManagerMapper;

    @Resource
    OrgInfoMapper orgInfoMapper;

    @Resource
    MmpPackNightActivityMapper mmpPackNightActivityMapper;

    @Resource
    BdpVehicleGradeMapper bdpVehicleGradeMapper;

    @Resource
    MembershipInfoMapper membershipInfoMapper;

    @Resource
    IMmpFreeDepositNumberService mmpFreeDepositNumberService;

    @Resource
    IMmpDiscountRuleService discountRuleService;

    @Resource
    IMmpPersonalDiscountService personalDiscountService;

    @Resource
    IMmpOperationLogService operationLogService;

    @Resource
    IMemAccountService memAccountService;

    @Resource
    private IAgencyService agencyService;

    @Resource
    private MmpAgencyDiscountLogMapper mmpAgencyDiscountLogMapper;

    @Resource
    private LongShortContractAdapter longShortContractAdapter;

    @Autowired
    private IMembershipAgencyService membershipAgencyService;

    /**
     * 关联企业下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<AgencyDTO> getAgencyAll(HttpServletRequest request) {
        List<AgencyDTO> agencyList = agencyInfoMapper.getAgencyAll();
        return agencyList;
    }

    /**
     * 渠道来源下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<AppDTO> getAppAll(AppDTO appDTO, HttpServletRequest request) {
        if (appDTO.getPlatKame() == null) {
            appDTO.setPlatKame("");
        }
        List<AppDTO> appList = appKeyManagerMapper.getAppAll(appDTO.getPlatKame());
        return appList;
    }

    /**
     * 会员所属公司下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<OrgInfoDTO> findAll(HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String orgId = comModel.getOrgId();
        List<OrgInfoDTO> orgList = orgInfoMapper.getOrgInfoById(orgId);
        return orgList;

    }

    @Override
    public List<OrgInfoDTO> findAllOrgs(HttpServletRequest request) {
        return orgInfoMapper.getOrgInfoById("00");
    }

    @Override
    public List<OrgInfoDTO> queryPlateInfoList(String orgId) {
        List<OrgInfoDTO> orgList = orgInfoMapper.getOrgInfoById(orgId);
        return orgList;

    }

    @Override
    public List<ActivityNameDTO> queryNightCarActivityList(HttpServletRequest request) {
        List<ActivityNameDTO> nightCarActivityList = mmpPackNightActivityMapper.queryNightCarActivityList();
        return nightCarActivityList;
    }

    /**
     * 免押等级下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<MmpDepositGradeDTO> queryExemptDepositGradeList(HttpServletRequest request) {
        List<MmpDepositGradeDTO> depositGradeList = bdpVehicleGradeMapper.getDepositGrade();
        return depositGradeList;
    }

    /**
     * 优惠券发放记录一览活动名称下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<String> queryActivityNameList(HttpServletRequest request) {
        List<String> list = mmpPackNightActivityMapper.queryActivityNameList();
        return list;
    }

    /**
     * 根据会员名称查询会员手机号
     *
     * @param name 会员名称
     * @return
     */
    @Override
    public List<String> queryTelByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            name = name.trim();
        }
        List<String> namelist = membershipInfoMapper.getTelByName(name);
        return namelist;
    }

    /**
     * 根据会员手机号查询会员名称
     *
     * @param mobilePhone 会员手机号
     * @return
     */
    @Override
    public List<String> queryNameByTel(String mobilePhone) {
        if (StringUtils.isNotBlank(mobilePhone)) {
            mobilePhone = mobilePhone.trim();
        }
        if (StringUtils.isBlank(mobilePhone)) {
            return null;
        }
        List<String> namelist = membershipInfoMapper.getNameByTel(mobilePhone);
        return namelist;
    }

    /**
     * 根据活动编号查询活动名称、类型、所属单位
     *
     * @param actionId 活动编号
     * @return
     */
    @Override
    public ActivityInfoDTO queryActivityInfoById(String actionId) {
        if (StringUtils.isNotBlank(actionId)) {
            actionId = actionId.trim();
        }
        ActivityInfoDTO activityInfoDTO = mmpPackNightActivityMapper.queryActivityInfoById(actionId);
        return activityInfoDTO;
    }

    /**
     * 根据活动名称、类型、所属单位查询活动编号
     *
     * @param activityName 活动编号
     * @param type         活动类型
     * @param orgId        所属单位
     * @return
     */
    @Override
    public List<String> queryActivityId(String activityName, Integer type, String orgId) {
        if (StringUtils.isNotBlank(activityName)) {
            activityName = activityName.trim();
        }
        if (StringUtils.isNotBlank(orgId)) {
            orgId = orgId.trim();
        }
        List<String> activityIdlist = mmpPackNightActivityMapper.getActivityId(activityName, type, orgId);
        return activityIdlist;
    }

    @Override
    public List<ActivityIdAndNameDTO> queryActivityIdAndName(String activityName, Integer type, String orgId) {
        if (StringUtils.isNotBlank(activityName)) {
            activityName = activityName.trim();
        }
        if (StringUtils.isNotBlank(orgId)) {
            orgId = orgId.trim();
        }
        List<ActivityIdAndNameDTO> activityIdAndName = mmpPackNightActivityMapper.getActivityIdAndName(activityName, type, orgId);
        return activityIdAndName;
    }


    @Override
    public PageBeanBO<AgencyInfoDTO> find(AgencyInfoSearchDTO agencyInfoSearchDTO) {
        PageBeanBO<AgencyInfoDTO> pageBeanBO = new PageBeanBO<>();
        PageHelper.startPage(agencyInfoSearchDTO.getPageNum(), agencyInfoSearchDTO.getPageSize(), false);
        List<AgencyInfo> agencyInfos = agencyInfoMapper.queryAgencyMember(agencyInfoSearchDTO);
        List<AgencyInfoDTO> agencyInfoDTOS = new ArrayList<>();
        if (agencyInfos.size() > 0) {
            for (AgencyInfo agencyInfo : agencyInfos) {
                AgencyInfoDTO agencyInfoDTO = new AgencyInfoDTO();
                BeanCopyUtils.copyProperties(agencyInfo, agencyInfoDTO);
                //20230512 享道安全检测出的漏洞修复
                agencyInfoDTO.setMobilePhone(ComUtil.getCommSecretMsg(agencyInfoDTO.getMobilePhone(), 3, 4));
                agencyInfoDTOS.add(agencyInfoDTO);
            }
            PageInfo<AgencyInfoDTO> pageInfo = new PageInfo<>(agencyInfoDTOS);
            if (1 == agencyInfoSearchDTO.getIsAll()) {
                int total = agencyInfoMapper.getAgencyMenberNum(agencyInfoSearchDTO);
                pageInfo.setTotal(total);
            }
            pageBeanBO.setList(agencyInfoDTOS);
            PageBO pageBO = new PageBO();
            BeanCopyUtils.copyProperties(pageInfo, pageBO);
            pageBeanBO.setPage(pageBO);
        }
        return pageBeanBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO save(AgencyInfoSaveDTO agencyInfoSaveDTO, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        AgencyInfo agencyInfo = new AgencyInfo();
        /** 获取最新的agencyId 并生成下一个agencyId*/
        String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
        String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2, maxAgencyId.length());
        String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + AgencyIdUtils.getNextNodeId(last2Digit);
        if (agencyInfoSaveDTO != null && newAgencyId != null) {
            // 企业免押金额 必须大于或者等于0
            BigDecimal exemptDepositAmount = agencyInfoSaveDTO.getExemptDepositAmount() == null ? BigDecimal.ZERO : agencyInfoSaveDTO.getExemptDepositAmount();
            if (exemptDepositAmount != null && exemptDepositAmount.compareTo(BigDecimal.ZERO) < 0) {
                return new DefaultServiceRespDTO(-1,"企业免押金额必须大于0");
            }

            // 企业推荐人校验
            if ((Optional.ofNullable(agencyInfoSaveDTO.getCreateWay()).orElse(0) == 1 && (StringUtils.isBlank(agencyInfoSaveDTO.getReferrerMobile()) || StringUtils.isBlank(agencyInfoSaveDTO.getReferrerName())))) {
                return new DefaultServiceRespDTO(-1,"推荐人和推荐人手机不能为空");
            }

            /** 新建企业默认初始化折扣信息 */
            Long discountId = discountRuleService.saveInit(newAgencyId, request,null, 100.0, 0);
            Long discountPersonId = discountRuleService.saveInitForPerson(newAgencyId, request, null, 100.0);
            discountRuleService.saveInitForPackage(newAgencyId, request, null);
            BeanCopyUtils.copyProperties(agencyInfoSaveDTO, agencyInfo);
            agencyInfo.setExemptDepositAmount(exemptDepositAmount);
            agencyInfo.setAgencyId(newAgencyId);
            agencyInfo.setDiscountId(discountId);
            agencyInfo.setDiscountPersonalId(discountPersonId);
//            agencyInfo.setDiscountPackageId(discountPackageId);
            agencyInfo.setDiscountRule(1);
            agencyInfo.setAppKey("");
            agencyInfo.setOrgName("");
            agencyInfo.setlongRentContractId("");
            //0-暂停中，1-合作中，2-未开始，新老状态并行。
            agencyInfo.setStatus(2);
            agencyInfo.setMaxUnitPrice(new BigDecimal(1));
            agencyInfo.setCooperateStatus(0);
            agencyInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfo.setCreatedUser(comModel.getCreateOperName());
            agencyInfo.setCreateOperId(comModel.getCreateOperId());
            agencyInfo.setUpdatedUser(comModel.getUpdateOperName());
            agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfo.setUpdateOperId(comModel.getUpdateOperId());
            if(agencyInfoSaveDTO.getVehicleNoLimit() != null) {
                String vehicleNo = StringUtils.join(agencyInfoSaveDTO.getVehicleNoLimit(), Contants.MARK_COMMA);
                agencyInfo.setVehicleNo(vehicleNo);
            }
            int number = addAgencyInfo(agencyInfo);

            if (number > 0) {
                /** 新建企业会员更新redis */
                String agencyRedisKey = "agencyFreeInfo_" + agencyInfo.getAgencyId();
                HashMap<String, String> agencyMap = new HashMap<>();

                agencyMap.put("pay_way", String.valueOf(agencyInfo.getPayWay().intValue()));
                JedisUtil.hmset(agencyRedisKey, agencyMap);
                /** 新增免押人数*/
                AgencyFreeDepositSaveDTO freeDepositSaveDTO = new AgencyFreeDepositSaveDTO();
                freeDepositSaveDTO.setAgencyId(newAgencyId);
                freeDepositSaveDTO.setFirstClassNumberLimit(agencyInfoSaveDTO.getFirstClassNumberLimit());
                freeDepositSaveDTO.setSecondClassNumberLimit(agencyInfoSaveDTO.getSecondClassNumberLimit());
                freeDepositSaveDTO.setThirdClassNumberLimit(agencyInfoSaveDTO.getThirdClassNumberLimit());
                freeDepositSaveDTO.setFourthClassNumberLimit(agencyInfoSaveDTO.getFourthClassNumberLimit());
                mmpFreeDepositNumberService.save(freeDepositSaveDTO, request,null);
                OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
                operationLogSaveDTO.setOperationRemark("新建企业会员");
                operationLogSaveDTO.setAgencyId(newAgencyId);
                operationLogSaveDTO.setOperationTime(new Date());
                operationLogService.save(operationLogSaveDTO, request, null);
            }
        }
        return DefaultServiceRespDTO.SUCCESS;
    }


    public int updateAgencyInfo(Integer operateType, AgencyInfo oldAgencyInfo, AgencyInfo newAgencyInfo) throws BusinessException {
        //创建方式 1会员系统 2政企框架合同
        // 编辑时校验
        if (ObjectUtils.compare(1, operateType) == 0 && ObjectUtils.compare(oldAgencyInfo.getCreateWay(), 2) == 0) {
            if (ComUtil.checkColUpdated(oldAgencyInfo.getAgencyName(), newAgencyInfo.getAgencyName())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getLicenseNo(), newAgencyInfo.getLicenseNo())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getPayWay(), newAgencyInfo.getPayWay())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getLineLimitMonthly(), newAgencyInfo.getLineLimitMonthly())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getDepositGuarantor(), newAgencyInfo.getDepositGuarantor())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getOrderPayer(), newAgencyInfo.getOrderPayer())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getDefaultingParty(), newAgencyInfo.getDefaultingParty())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getCreateWay(), newAgencyInfo.getCreateWay())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getBusinessSource(), newAgencyInfo.getBusinessSource())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getExpireTime(), newAgencyInfo.getExpireTime())
                    || oldAgencyInfo.getExemptDepositAmount().compareTo(newAgencyInfo.getExemptDepositAmount()) != 0
                    || ComUtil.checkColUpdated(oldAgencyInfo.getDiscountRule(), newAgencyInfo.getDiscountRule())
            ) {
                throw new BusinessException("企业名称、企业执照、结算方式、月额度限制、押金担保方、订单支付方、违约承担方、企业创建方式、业务来源、失效时间、企业免押金额、折扣规则不可编辑");
            }
            else if (ObjectUtils.compare(oldAgencyInfo.getCooperateStatus(), 1) == 0 && ObjectUtils.compare(newAgencyInfo.getCooperateStatus(), 2) == 0) {
                throw new BusinessException("合作中不可暂停企业会员合作关系");
            }
            else if ((ObjectUtils.compare(oldAgencyInfo.getCooperateStatus(), 0) == 0 || ObjectUtils.compare(oldAgencyInfo.getCooperateStatus(), 2) == 0)
                    && ObjectUtils.compare(newAgencyInfo.getCooperateStatus(), 1) == 0) {
                throw new BusinessException("暂停中/未开始状态不可开始合作");
            }
        }
        else if (ObjectUtils.compare(1, operateType) == 0 && ObjectUtils.compare(oldAgencyInfo.getCreateWay(), 1) == 0) {
            if (ComUtil.checkColUpdated(oldAgencyInfo.getPayWay(), newAgencyInfo.getPayWay())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getDepositGuarantor(), newAgencyInfo.getDepositGuarantor())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getOrderPayer(), newAgencyInfo.getOrderPayer())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getDefaultingParty(), newAgencyInfo.getDefaultingParty())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getCreateWay(), newAgencyInfo.getCreateWay())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getBusinessSource(), newAgencyInfo.getBusinessSource())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getReferrerMobile(), newAgencyInfo.getReferrerMobile())
                    || ComUtil.checkColUpdated(oldAgencyInfo.getReferrerName(), newAgencyInfo.getReferrerName())
                    || oldAgencyInfo.getExemptDepositAmount().compareTo(newAgencyInfo.getExemptDepositAmount()) != 0
            ) {
                throw new BusinessException("结算方式、押金担保方、订单支付方、违约承担方、企业创建方式、业务来源、企业免押金额、推荐人、推荐人手机号不可编辑");
            }
            else if ((oldAgencyInfo.getCooperateStatus() == 0 || oldAgencyInfo.getCooperateStatus() == 2) && !newAgencyInfo.getExpireTime().after(new Date())) {
                throw new BusinessException("暂停中/未开始状态会员开始时，失效时间需要晚于当前时间");
            }
        }

        int number = agencyInfoMapper.updateByIdSelective(newAgencyInfo);
        //若涉及到折扣相关的字段被更新，则添加企业折扣信息变更履历
        if(ComUtil.checkColUpdated(oldAgencyInfo.getCooperateStartTime(), oldAgencyInfo.getCooperateStartTime())
                || ComUtil.checkColUpdated(oldAgencyInfo.getCooperateEndTime(), oldAgencyInfo.getCooperateEndTime())
                || ComUtil.checkColUpdated(oldAgencyInfo.getCooperateStatus(), newAgencyInfo.getCooperateStatus())
                || ComUtil.checkColUpdated(oldAgencyInfo.getStatus(), newAgencyInfo.getStatus())
                || !vehicleNoEqual(oldAgencyInfo.getVehicleNo(), newAgencyInfo.getVehicleNo())
                || ComUtil.checkColUpdated(oldAgencyInfo.getDiscountId(), newAgencyInfo.getDiscountId())
                || ComUtil.checkColUpdated(oldAgencyInfo.getDiscountPersonalId(), newAgencyInfo.getDiscountPersonalId()))  {
            AgencyInfo agencyInfo = agencyInfoMapper.selectById(newAgencyInfo.getAgencyId());
            MmpAgencyDiscountLog record = new MmpAgencyDiscountLog();
            BeanCopyUtils.copyProperties(agencyInfo, record);
            record.setCreateTime(new Date());
            record.setCreateOperId(newAgencyInfo.getUpdateOperId());
            record.setCreateOperName(newAgencyInfo.getUpdatedUser());
            record.setUpdateOperId(null);
            record.setUpdateOperName(null);
            record.setUpdateTime(null);
            record.setOperateType(operateType);
            mmpAgencyDiscountLogMapper.insertSelective(record);
        }
        return number;
    }

    public int addAgencyInfo(AgencyInfo agencyInfo){
        int number = agencyInfoMapper.save(agencyInfo);
        MmpAgencyDiscountLog record = new MmpAgencyDiscountLog();
        BeanCopyUtils.copyProperties(agencyInfo, record);
        record.setCreateTime(new Date());
        record.setCreateOperId(agencyInfo.getCreateOperId());
        record.setCreateOperName(agencyInfo.getCreatedUser());
        record.setOperateType(0);
        mmpAgencyDiscountLogMapper.insertSelective(record);
        return number;
    }

    private boolean vehicleNoEqual(String oldValue, String newValue){
        oldValue = (StringUtils.isBlank(oldValue)) ? StringUtils.EMPTY : oldValue;
        newValue = (StringUtils.isBlank(newValue)) ? StringUtils.EMPTY : newValue;
        if(!ComUtil.checkColUpdated(oldValue, newValue)){
            return true;
        }
        List<String> oldArr = Arrays.asList(StringUtils.split(oldValue,","));
        List<String> newArr = Arrays.asList(StringUtils.split(newValue,","));
        //return CollectionUtils.isEqualCollection(oldArr, newArr);
        //null与空集合认为相同
        return ComUtil.eualCollection(oldArr, newArr);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO update(AgencyInfoUpdateDTO agencyInfoUpdateDTO, HttpServletRequest request, OperatorDTO operatorDTO) {
        ComModel comModel;
        if (request != null) {
            comModel = ComUtil.getUserInfo(request);
        }
        else {
            comModel = ComUtil.getUserInfo(operatorDTO);
        }
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        /** 获取最新的agencyId 并生成下一个agencyId*/
        if (agencyInfoUpdateDTO != null) {
            // 企业免押金额不为空时，必须大于或者等于0
            BigDecimal exemptDepositAmount = agencyInfoUpdateDTO.getExemptDepositAmount() == null ? BigDecimal.ZERO:agencyInfoUpdateDTO.getExemptDepositAmount() ;
            if (exemptDepositAmount != null && exemptDepositAmount.compareTo(BigDecimal.ZERO) < 0) {
                return new DefaultServiceRespDTO(-1,"企业免押金额必须大于0");
            }

            AgencyInfo oldAgencyInfo = agencyInfoMapper.selectById(agencyInfoUpdateDTO.getAgencyId());
            AgencyInfo newAgencyInfo = new AgencyInfo();
            BeanCopyUtils.copyProperties(oldAgencyInfo, newAgencyInfo);
            if (newAgencyInfo != null && newAgencyInfo.getStatus() != 1) {
                BeanCopyUtils.copyProperties(agencyInfoUpdateDTO, newAgencyInfo);
                newAgencyInfo.setUpdateOperId(comModel.getCreateOperId());
                newAgencyInfo.setUpdatedUser(comModel.getUpdateOperName());
                newAgencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
                newAgencyInfo.setExemptDepositAmount(exemptDepositAmount);
                if(agencyInfoUpdateDTO.getVehicleNoLimit() != null) {
                    String vehicleNo = StringUtils.join(agencyInfoUpdateDTO.getVehicleNoLimit(), Contants.MARK_COMMA);
                    newAgencyInfo.setVehicleNo(vehicleNo);
                }
                //更新机构信息和履历
                //int number = agencyInfoMapper.updateByIdSelective(newAgencyInfo);
                int number = 0;
                try {
                    number = updateAgencyInfo(1, oldAgencyInfo, newAgencyInfo);
                }
                catch (BusinessException e) {
                    return new DefaultServiceRespDTO(1, e.getMessage());
                }


                if (number > 0) {
                    /** 新建企业会员更新redis */
                    String agencyRedisKey = "agencyFreeInfo_" + newAgencyInfo.getAgencyId();
                    JedisUtil.hset(agencyRedisKey, "pay_way", String.valueOf(newAgencyInfo.getPayWay().intValue()));
                    /** 修改免押人数*/
                    AgencyFreeDepositUpdateDTO freeDepositUpdateDTO = new AgencyFreeDepositUpdateDTO();
                    AgencyFreeDepositDetailDTO freeDepositDetailDTO = mmpFreeDepositNumberService.getLimit(newAgencyInfo.getAgencyId());
                    if (freeDepositDetailDTO != null) {
                        freeDepositUpdateDTO.setId(freeDepositDetailDTO.getId());
                        freeDepositUpdateDTO.setAgencyId(freeDepositDetailDTO.getAgencyId());
                        freeDepositUpdateDTO.setFirstClassNumberLimit(agencyInfoUpdateDTO.getFirstClassNumberLimit());
                        freeDepositUpdateDTO.setSecondClassNumberLimit(agencyInfoUpdateDTO.getSecondClassNumberLimit());
                        freeDepositUpdateDTO.setThirdClassNumberLimit(agencyInfoUpdateDTO.getThirdClassNumberLimit());
                        freeDepositUpdateDTO.setFourthClassNumberLimit(agencyInfoUpdateDTO.getFourthClassNumberLimit());
                    } else {
                        AgencyFreeDepositSaveDTO freeDepositSaveDTO = new AgencyFreeDepositSaveDTO();
                        freeDepositSaveDTO.setAgencyId(newAgencyInfo.getAgencyId());
                        freeDepositSaveDTO.setFirstClassNumberLimit(agencyInfoUpdateDTO.getFirstClassNumberLimit());
                        freeDepositSaveDTO.setSecondClassNumberLimit(agencyInfoUpdateDTO.getSecondClassNumberLimit());
                        freeDepositSaveDTO.setThirdClassNumberLimit(agencyInfoUpdateDTO.getThirdClassNumberLimit());
                        freeDepositSaveDTO.setFourthClassNumberLimit(agencyInfoUpdateDTO.getFourthClassNumberLimit());
                        mmpFreeDepositNumberService.save(freeDepositSaveDTO, request,null);
                    }
                    mmpFreeDepositNumberService.update(freeDepositUpdateDTO, request);
                    OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
                    operationLogSaveDTO.setOperationRemark("修改企业会员基础信息");
                    operationLogSaveDTO.setOperationTime(new Date());
                    operationLogSaveDTO.setAgencyId(newAgencyInfo.getAgencyId());
                    operationLogService.save(operationLogSaveDTO, request, null);
                }
            } else {
                respDTO = new DefaultServiceRespDTO(1, "合作中的的机构不可以修改，请检查后重新操作！");
            }
            respDTO = new DefaultServiceRespDTO(0, "修改成功");
        } else {
            respDTO = new DefaultServiceRespDTO(1, "修改失败");
        }
        return respDTO;
    }

    @Override
    public AgencyInfoDeatailDTO get(String agencyId) {
        AgencyInfo agencyInfo = agencyInfoMapper.selectById(agencyId);
        if(agencyInfo == null) {
            return null;
        }
        AgencyInfoDeatailDTO agencyInfoDeatailDTO = new AgencyInfoDeatailDTO();
        BeanCopyUtils.copyProperties(agencyInfo, agencyInfoDeatailDTO);
        if(StringUtils.isNotBlank(agencyInfo.getVehicleNo())) {
            String[] vehicleNos = agencyInfo.getVehicleNo().split(Contants.MARK_COMMA);
            agencyInfoDeatailDTO.setVehicleNoLimit(Arrays.asList(vehicleNos));
        }
        AgencyFreeDepositDetailDTO agencyFreeDepositDetailDTO = mmpFreeDepositNumberService.get(agencyId);
        if (agencyFreeDepositDetailDTO != null) {
            BeanCopyUtils.copyProperties(agencyFreeDepositDetailDTO, agencyInfoDeatailDTO);
        }
        return agencyInfoDeatailDTO;
    }

    @Override
    public DefaultServiceRespDTO updateStartCooperate(String agencyId, HttpServletRequest request, OperatorDTO operatorDTO) {
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        ComModel comModel;
        if (request != null) {
            comModel = ComUtil.getUserInfo(request);
        }
        else {
            comModel = ComUtil.getUserInfo(operatorDTO);
        }
        AgencyInfo agencyInfo = agencyInfoMapper.selectById(agencyId);
        if (agencyInfo != null && agencyInfo.getStatus() != 1) {
            AgencyInfo updateAgencyInfo = new AgencyInfo();
            updateAgencyInfo.setAgencyId(agencyInfo.getAgencyId());
            updateAgencyInfo.setCooperateStatus(1);
            updateAgencyInfo.setStatus(1);
            updateAgencyInfo.setCooperateStartTime(new Date());
            updateAgencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            updateAgencyInfo.setUpdatedUser(comModel.getCreateOperName());
            updateAgencyInfo.setUpdateOperId(comModel.getCreateOperId());
            //更新机构信息
            //agencyInfoMapper.updateByIdSelective(updateAgencyInfo);
            try {
                updateAgencyInfo(2, agencyInfo, updateAgencyInfo);
            }
            catch (BusinessException e) {
                return new DefaultServiceRespDTO(1, e.getMessage());
            }
            respDTO = DefaultServiceRespDTO.SUCCESS;
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("企业会员开始合作,合作开始时间为：" + ComUtil.getSystemDate(ComUtil.DATE_TYPE5));
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, operatorDTO);
        } else {
            respDTO = new DefaultServiceRespDTO(1, "未找到该企业会员或该企业会员处于未开始合作阶段，不可开始合作");
        }
        return respDTO;
    }

    @Override
    public DefaultServiceRespDTO updatePauseCooperate(String agencyId, HttpServletRequest request, OperatorDTO operatorDTO) {
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        ComModel comModel;
        if (request != null) {
            comModel = ComUtil.getUserInfo(request);
        }
        else {
            comModel = ComUtil.getUserInfo(operatorDTO);
        }
        AgencyInfo agencyInfo = agencyInfoMapper.selectById(agencyId);
        if (agencyInfo != null && agencyInfo.getStatus() == 1) {
            AgencyInfo agencyInfoUpdate = new AgencyInfo();
            agencyInfoUpdate.setAgencyId(agencyInfo.getAgencyId());
            agencyInfoUpdate.setCooperateStatus(2);
            agencyInfoUpdate.setStatus(0);
            agencyInfoUpdate.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfoUpdate.setUpdateOperId(comModel.getCreateOperId());
            agencyInfoUpdate.setUpdatedUser(comModel.getCreateOperName());
            //更新机构信息
            //agencyInfoMapper.updateByIdSelective(updateAgencyInfo);
            try {
                updateAgencyInfo(3, agencyInfo, agencyInfoUpdate);
            }
            catch (BusinessException e) {
                return new DefaultServiceRespDTO(1, e.getMessage());
            }
            respDTO = DefaultServiceRespDTO.SUCCESS;
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("企业会员暂停合作，合作暂停时间为：" + ComUtil.getSystemDate(ComUtil.DATE_TYPE5));
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, operatorDTO);
        } else {
            respDTO = new DefaultServiceRespDTO(1, "未找到该企业会员或该企业会员处于未开始合作阶段，不可暂停合作");
        }
        return respDTO;
    }

    @Override
    public BvmAgencyInfoDTO getBvmAgencyInfo(String agencyId) {
        BvmAgencyInfoDTO bvmAgencyInfoDTO = new BvmAgencyInfoDTO();
        AgencyInfo agencyInfo = agencyInfoMapper.selectById(agencyId);
        if (agencyInfo != null) {
            BeanCopyUtils.copyProperties(agencyInfo, bvmAgencyInfoDTO);
            AgencyFreeDepositDetailDTO agencyFreeDepositDetailDTO = mmpFreeDepositNumberService.get(agencyId);
            if (agencyFreeDepositDetailDTO != null) {
                BeanCopyUtils.copyProperties(agencyFreeDepositDetailDTO, bvmAgencyInfoDTO);
                bvmAgencyInfoDTO.setLevel0Count(agencyFreeDepositDetailDTO.getZeroClassNumberLimit());
                bvmAgencyInfoDTO.setLevel1Count(agencyFreeDepositDetailDTO.getFirstClassNumber());
                bvmAgencyInfoDTO.setLevel2Count(agencyFreeDepositDetailDTO.getSecondClassNumber());
                bvmAgencyInfoDTO.setLevel3Count(agencyFreeDepositDetailDTO.getThirdClassNumber());
                bvmAgencyInfoDTO.setLevel4Count(agencyFreeDepositDetailDTO.getFourthClassNumber());
            }
            DiscountRuleDTO discontRule = discountRuleService.get(agencyId, 0);
            DiscountRuleDTO personalDiscontRule = discountRuleService.get(agencyId, 1);
            if (discontRule != null) {
                bvmAgencyInfoDTO.setAgencyDiscountRule(discontRule);
            }
            if (personalDiscontRule != null) {
                bvmAgencyInfoDTO.setPersonalDiscoutRule(personalDiscontRule);
            }
        }
        return bvmAgencyInfoDTO;
    }

    @Override
    @Transactional
    public DefaultServiceRespDTO presentEAmountForOrg(MemberShipAccountDTO memberShipAccountDTO, String agencyId,
                                                      HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        //Long createOperId = comModel.getCreateOperId();
        String expireDate = StringUtils.EMPTY;
        if (memberShipAccountDTO == null) {
            respDTO.setCode(-1);
            respDTO.setMessage("参数不能为空");
            return respDTO;
        }
        if (memberShipAccountDTO.getAmount() == null ||
                memberShipAccountDTO.getAmount() < 1 || memberShipAccountDTO.getAmount() > 999999) {
            respDTO.setCode(-1);
            respDTO.setMessage("赠送数量必须在1-999999的整数");
            return respDTO;
        }
        try {
            Calendar calendar = Calendar.getInstance();
            if (memberShipAccountDTO.getTimeType() == 0) {
                Date date = DateFormatUtils.ISO_DATE_FORMAT.parse(memberShipAccountDTO.getExpireDate());
                calendar.setTime(date);
            } else if (memberShipAccountDTO.getTimeType() == 1) {
                calendar.add(Calendar.MONTH, 3);
            } else if (memberShipAccountDTO.getTimeType() == 2) {
                calendar.add(Calendar.MONTH, 6);
            } else if (memberShipAccountDTO.getTimeType() == 3) {
                calendar.add(Calendar.YEAR, 1);
            } else if (memberShipAccountDTO.getTimeType() == 4) {
                calendar.add(Calendar.YEAR, 3);
            }
            expireDate = DateFormatUtils.ISO_DATE_FORMAT.format(calendar);
        } catch (Exception e) {
            e.printStackTrace();
            respDTO.setCode(-1);
            respDTO.setMessage("到账日期格式错误");
            return respDTO;
        }
        BigDecimal amount = new BigDecimal(memberShipAccountDTO.getAmount());
        // 调用财务赠送E币服务
        log.info("调用财务赠送E币服务");
        Map<String, Object> returnMap = memAccountService.presentEAmountForOrg(agencyId, amount, null,
                expireDate, createOperName, 1);
        if (returnMap == null) {
            respDTO.setCode(-1);
            respDTO.setMessage("调用财务赠送E币服务失败");
            return respDTO;
        }
        int status = (int) returnMap.get("status");
        String msg = (String) returnMap.get("msg");
        if (status == 0) {
           /* EAmountBalanceBO balance = memAccountService.queryEAmountBalance(agencyId);
            BigDecimal chargeBalance = BigDecimal.ZERO;
            if (balance != null) {
                chargeBalance = balance.getChargeBalance();
            }*/
            AgencyInfoDeatailDTO agencyInfoDeatailDTO = agencyService.get(agencyId);
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("赠送数量" + amount + "个，E币账户剩余" + agencyInfoDeatailDTO.getRentMins() + "个");
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, null);
        }
        respDTO.setCode(status);
        respDTO.setMessage(msg);
        return respDTO;

    }

    @Override
    @Transactional
    public DefaultServiceRespDTO deductPresentEAmountForOrg(MemberShipAccountDTO memberShipAccountDTO, String agencyId,
                                                            HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        //Long createOperId = comModel.getCreateOperId();
        if (memberShipAccountDTO == null) {
            respDTO.setCode(-1);
            respDTO.setMessage("参数不能为空");
            return respDTO;
        }
        String amountInfoSeq = memberShipAccountDTO.getAmountInfoSeq();
        if (StringUtils.isBlank(amountInfoSeq)) {
            respDTO.setCode(-1);
            respDTO.setMessage("E币赠送记录唯一标识不能为空");
            return respDTO;
        }
        if (null == memberShipAccountDTO.getAmount()) {
            respDTO.setCode(-1);
            respDTO.setMessage("扣除数量不能为空");
            return respDTO;
        }
        if (memberShipAccountDTO.getAmount() < 1) {
            respDTO.setCode(-1);
            respDTO.setMessage("扣除数量不满足要求");
            return respDTO;
        }
        BigDecimal amount = new BigDecimal(memberShipAccountDTO.getAmount());
        if (StringUtils.isNotBlank(memberShipAccountDTO.getRemark())
                && memberShipAccountDTO.getRemark().length() > 100) {
            respDTO.setCode(-1);
            respDTO.setMessage("扣除原因应在100字以内");
            return respDTO;
        }
        // 调用财务扣除赠送E币服务
        log.info("调用财务扣除E币服务");
        Map<String, Object> returnMap = memAccountService.deductPresentEAmountForOrg(amountInfoSeq, agencyId, amount,
                memberShipAccountDTO.getRemark(), createOperName, 1);
        if (returnMap == null) {
            respDTO.setCode(-1);
            respDTO.setMessage("调用财务赠送E币服务失败");
            return respDTO;
        }
        int status = (int) returnMap.get("status");
        if (status == 0) {
           /* EAmountBalanceBO balance = memAccountService.queryEAmountBalance(agencyId);
            BigDecimal chargeBalance = BigDecimal.ZERO;
            if (balance != null) {
                chargeBalance = balance.getChargeBalance();
            }*/
            AgencyInfoDeatailDTO agencyInfoDeatailDTO = agencyService.get(agencyId);
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("扣除数量" + amount + "个，E币账户剩余" + agencyInfoDeatailDTO.getRentMins() + "个，扣除原因："
                    + memberShipAccountDTO.getRemark());
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, null);
        }
        String msg = (String) returnMap.get("msg");
        respDTO.setCode(status);
        respDTO.setMessage(msg);
        return respDTO;
    }

    @Override
    public PageBeanBO<AgencyMemberDTO> getAgencyMemberList(String agencyId, Integer pageNum, Integer pageSize, Integer isAll) {
        PageBeanBO<AgencyMemberDTO> pageBeanBO = new PageBeanBO<>();
        // 调用bvm接口查询员工列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agencyId", agencyId);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);
        JSONObject result = HttpUtils.httpPostWithJSON(PropertyUtils.getProperty("url.getAgencyMemberList"), paramMap);
        log.info("调用bvm查询员工列表接口返回：" + JSONObject.toJSONString(result));
        if (result != null && "0".equals(result.get("code"))) {
            JSONObject data = (JSONObject) result.get("data");
            if (data != null) {
                List<AgencyMemberDTO> list = (List<AgencyMemberDTO>) JSONArray
                        .parseArray(JSON.toJSONString(data.get("list")), AgencyMemberDTO.class);
                if (CollectionUtils.isEmpty(list) || list.size() < 1) {
                    return pageBeanBO;
                }
                PageInfo<AgencyMemberDTO> pageInfo = new PageInfo<>(list);
                pageBeanBO.setList(list);
                PageBO pageBO = new PageBO();
                BeanCopyUtils.copyProperties(pageInfo, pageBO);
                if (null != isAll && isAll == 1) {
                    pageBO.setTotal((long) data.get("total"));
                }
                pageBeanBO.setPage(pageBO);
                return pageBeanBO;
            }
        }
        return pageBeanBO;
    }

    @Override
    public AgencyRoleSpecDTO getAgencyRoleSpec(String agencyRoleId) {
        // 调用bvm接口查询员工列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agencyRoleId", agencyRoleId);
        JSONObject result = HttpUtils.httpPostWithJSON(PropertyUtils.getProperty("url.getAgencyRoleSpec"), paramMap);
        log.info("调用bvm查询员工列表接口返回：" + JSONObject.toJSONString(result));
        if (result != null && "0".equals(result.get("code"))) {
            AgencyRoleSpecDTO dto = JSONObject.parseObject(JSON.toJSONString(result.get("data")), AgencyRoleSpecDTO.class);
            List<VehicleHourDTO> restrictTime = dto.getRestrictTime();
            if (CollectionUtils.isNotEmpty(restrictTime)) {
                for (VehicleHourDTO vehicleHourDTO : restrictTime) {
                    vehicleHourDTO.setStartTime(ComUtil.getFormatDate(vehicleHourDTO.getStartTime(), "HHmm", "HH:mm"));
                    vehicleHourDTO.setEndTime(ComUtil.getFormatDate(vehicleHourDTO.getEndTime(), "HHmm", "HH:mm"));
                }
            }
            return dto;
        }
        return null;
    }

    @Override
    public BaseResponse agencyInfoListenerFromLongShort(AgencyInfoSyncInput input) {
        return longShortContractAdapter.handleMsgFromLongShortContract(input);
    }

    @Resource
    private ILongRentMemberSyncService longRentMemberSyncService;

    @Override
    public BaseResponse longRentMemberSync(LongRentMemberSyncInput input) {
        return longRentMemberSyncService.syncLongRentMember(input);
    }

    /**
     * 新增企业与二级渠道关联关系
     *
     * @param input   请求参数封装对象
     * @param request 请求对象（用于获取操作人信息）
     * @return DefaultServiceRespDTO 操作结果
     */
    @Override
    public DefaultServiceRespDTO addAgencySecondAppKeyRelation(AddAgencySecondAppKeyRelationInput input, HttpServletRequest request) {
        log.info("开始新增企业与二级渠道关联关系，参数={}", JSON.toJSONString(input));
        ComModel comModel = ComUtil.getUserInfo(request);
        if (input == null) {
            return new DefaultServiceRespDTO(-1, "参数不能为空");
        }

        String agencyId = input.getAgencyId();
        String secondAppKey = input.getSecondAppKey();
        String secondAppKeyName = input.getSecondAppKeyName();
        String agencyRoleName = input.getAgencyRoleName();
        Long agencyRoleId = input.getAgencyRoleId();

        // 参数校验
        if (StringUtils.isBlank(agencyId)) {
            return new DefaultServiceRespDTO(-1, "企业ID不能为空");
        }
        if (StringUtils.isBlank(secondAppKey)) {
            return new DefaultServiceRespDTO(-1, "二级渠道key不能为空");
        }

        if (StringUtils.isBlank(secondAppKeyName)) {
            return new DefaultServiceRespDTO(-1, "二级渠道名称不能为空");
        }
        if (StringUtils.isBlank(agencyRoleName)) {
            return new DefaultServiceRespDTO(-1, "用车规则名称不能为空");
        }
        if (agencyRoleId == null || agencyRoleId <= 0) {
            return new DefaultServiceRespDTO(-1, "用车规则不能为空");
        }


        try {
            // 调用 service 添加关联关系
            AddAgencySecondAppKeyRelationDTO dto = new AddAgencySecondAppKeyRelationDTO();
            BeanUtils.copyProperties(input, dto);
            OperatorDto operatorDto = new OperatorDto();
            operatorDto.setOperatorName(comModel.getCreateOperName());
            operatorDto.setOperatorId(comModel.getCreateOperId());
            operatorDto.setOriginSystem("会员系统");
            dto.setOperator(operatorDto);
            CommonAddRespDto response = membershipAgencyService.addAgencySecondAppKeyRelation(dto);
            if (response == null || response.getCode() != 0) {
                log.error("新增企业与二级渠道关联失败，返回码：{}, message: {}",
                        response != null ? response.getCode() : "null",
                        response != null ? response.getMessage() : "null");
                return new DefaultServiceRespDTO(-1, "新增关联失败：" + (response != null ? response.getMessage() : "未知错误"));
            }

            // 记录操作日志
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("生成渠道下单码，二级渠道名称：" + secondAppKeyName + ", 渠道KEY=" + secondAppKey + "，用车规则：" + agencyRoleName);
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, null);
            log.info("新增企业与二级渠道关联成功，agencyId={}, input={}", agencyId, JSON.toJSONString(input));
            return DefaultServiceRespDTO.SUCCESS;
        } catch (Exception e) {
            log.error("新增企业与二级渠道关联异常,input={}", JSON.toJSONString(input), e);
            return new DefaultServiceRespDTO(-1, "系统异常，新增失败");
        }
    }

    /**
     * 删除企业与二级渠道关联关系
     *
     * @param agencyId  企业id
     * @param request 请求对象（用于获取操作人信息）
     * @return DefaultServiceRespDTO 操作结果
     */
    @Override
    public DefaultServiceRespDTO deleteAgencySecondAppKeyRelation(String agencyId, HttpServletRequest request) {
        log.info("开始删除企业与二级渠道关联关系，参数={}", agencyId);
        ComModel comModel = ComUtil.getUserInfo(request);
        // 参数校验
        if (StringUtils.isBlank(agencyId)) {
            return new DefaultServiceRespDTO(-1, "企业ID不能为空");
        }

        try {
            CommBaseResponse<AgencySecondAppKeyRelationDTO> agencySecondAppKeyRelationDTOCommBaseResponse = membershipAgencyService.queryAgencySecondAppKeyRelation(agencyId);
            if (agencySecondAppKeyRelationDTOCommBaseResponse.getCode() != 0) {
                log.error("查询企业与二级渠道关联关系失败，membershipAgencyService.deleteAgencySecondAppKeyRelation：{}", JSON.toJSONString(agencySecondAppKeyRelationDTOCommBaseResponse));
                return new DefaultServiceRespDTO(-1, "未查询到渠道下单码信息");
            }
            AgencySecondAppKeyRelationDTO agencySecondAppKeyRelationDTO = agencySecondAppKeyRelationDTOCommBaseResponse.getData();
            String secondAppKey = agencySecondAppKeyRelationDTO.getSecondAppKey();
            String agencyRoleName = agencySecondAppKeyRelationDTO.getAgencyRoleName();
            String secondAppKeyName = agencySecondAppKeyRelationDTO.getSecondAppKeyName();

            OperatorDto operatorDto = new OperatorDto();
            operatorDto.setOperatorName(comModel.getCreateOperName());
            operatorDto.setOperatorId(comModel.getCreateOperId());
            operatorDto.setOriginSystem("会员系统");
            // 调用 service 删除关联关系
            BaseResponse response = membershipAgencyService.deleteAgencySecondAppKeyRelation(agencyId, operatorDto);
            if (response.getCode() != 0) {
                log.error("删除企业与二级渠道关联失败，response：{},", JSON.toJSONString(response));
                return new DefaultServiceRespDTO(-1, "删除关联失败：" + response.getMessage());
            }

            // 记录操作日志
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("删除渠道下单码，二级渠道名称：" + secondAppKeyName + ", 渠道KEY=" + secondAppKey + "，用车规则：" + agencyRoleName);
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogSaveDTO.setAgencyId(agencyId);
            operationLogService.save(operationLogSaveDTO, request, null);

            log.info("删除企业与二级渠道关联成功，agencyId={}, secondAppKey={}", agencyId, secondAppKey);
            return DefaultServiceRespDTO.SUCCESS;

        } catch (Exception e) {
            log.error("删除企业与二级渠道关联异常", e);
            return new DefaultServiceRespDTO(-1, "系统异常，删除失败");
        }
    }

    @Override
    public DefaultWebRespVO queryAgencySecondAppKeyRelation(String agencyId) {
        // 参数校验
        if (StringUtils.isBlank(agencyId)) {
            return new DefaultWebRespVO("-1", "企业ID不能为空");
        }

        CommBaseResponse<AgencySecondAppKeyRelationDTO> response = membershipAgencyService.queryAgencySecondAppKeyRelation(agencyId);
        if (response != null) {
            log.info("查询企业与二级渠道关联关系成功，agencyId={}, response={}", agencyId, JSON.toJSONString(response));
            if (response.getCode() == 0) {
                AgencySecondAppKeyRelationDTO data = response.getData();
                return DefaultWebRespVO.getSuccessVO(data);
            } else {
                return new DefaultWebRespVO(String.valueOf(response.getCode()), response.getMessage());
            }
        } else {
            return new DefaultWebRespVO("-1", "查询渠道下单码信息失败");
        }

    }


}




